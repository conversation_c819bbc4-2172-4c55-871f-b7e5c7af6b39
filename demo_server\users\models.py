from django.db import models
from django.contrib.auth.models import User

# Create your models here.

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    RECOGNIZER_CHOICES = [
        ('arcface', 'ArcFaceRecognizer'),
        ('facenet', 'FaceNetRecognizer'),
    ]
    preferred_recognizer = models.CharField(
        max_length=10,
        choices=RECOGNIZER_CHOICES,
        default='arcface',
    )
    is_organizational_admin = models.BooleanField(default=False)

    def __str__(self):
        return self.user.username

