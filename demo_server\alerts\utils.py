import cv2
import numpy as np
from PIL import Image
from django.conf import settings
from recognition.arcface_recognizer import ArcFace<PERSON>ecognizer
from recognition.facenet_recognizer import Face<PERSON><PERSON><PERSON>ognizer

# Initialize the recognizers
arcface_recognizer = ArcFaceRecognizer()
facenet_recognizer = FaceNetRecognizer()

def calculate_image_vector(image_path, model_type='arcface'):
    """
    Calculate image embedding vector using the specified model (arcface or facenet)
    """
    img = cv2.imread(image_path)
    
    if model_type == 'arcface':
        bboxes, _, vector = arcface_recognizer.recognize(img, threshold=0.0)
        if len(vector) > 0:
            return vector[0].tolist()
    elif model_type == 'facenet':
        bboxes, faces = facenet_recognizer.detect_faces(img, threshold=0.0)
        if len(bboxes) > 0:
            embedding = facenet_recognizer.get_face_embeddings(faces)[0]
            return embedding.tolist()
            
    return None

def detect_faces(image):
    # Use FaceNet recognizer to detect faces
    bboxes, faces = facenet_recognizer.detect_faces(image)
    print(f"bboxes: {bboxes}")
    return bboxes, faces

def preprocess_image(image_path):
    img = cv2.imread(image_path)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img = Image.fromarray(img)
    img = img.resize((112, 112))  # ArcFace input size
    img = np.array(img)
    img = np.transpose(img, (2, 0, 1))
    img = np.expand_dims(img, axis=0)
    return img.astype(np.float32)

def recognize_face(image):
    # Use ArcFace recognizer to recognize faces
    bboxes, names, scores = arcface_recognizer.recognize(image)
    return bboxes, names, scores

def add_face(name, filepath):
    # Add face to both recognizers
    arcface_recognizer.add_face(name, filepath)
    facenet_recognizer.add_face(name, filepath)