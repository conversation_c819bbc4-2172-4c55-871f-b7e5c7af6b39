{% extends 'base.html' %}

{% block content %}
<div class="alert-listing-container">
<h2>Persons</h2>
<div class="d-flex gap-2 mb-3">
    <a href="{% url 'alerts:add_person' %}" class="btn btn-primary">Add New Person</a>
    <button type="button" id="delete-selected-btn" class="btn btn-danger" disabled>
        <i class="bi bi-trash"></i> Delete Selected
    </button>
</div>

<form id="person-form" method="post" action="{% url 'alerts:delete_selected_persons' %}">
    {% csrf_token %}
    <table class="table table-striped">
        <thead>
            <tr>
                <th>
                    <input type="checkbox" id="select-all-checkbox" class="form-check-input">
                </th>
                <th>Name</th>
                <th>Number of Photos</th>
                <th>Number of Alerts</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for person in persons %}
            <tr>
                <td>
                    <input type="checkbox" name="selected_persons" value="{{ person.id }}" 
                           class="form-check-input person-checkbox">
                </td>
                <td><a href="{% url 'alerts:person_detail' person.id %}">{{ person.name }}</a></td>
                <td>{{ person.photo_count }}</td>
                <td>{{ person.alarm_count }}</td>
                <td>
                    <a href="{% url 'alerts:person_detail' person.id %}" class="btn btn-sm btn-info">View Details</a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5">No persons added yet.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Confirmation Modal -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteConfirmModalLabel">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete the selected person(s)? 
                    This will delete all related photos, alerts and face recognition data.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- JS kodu doğrudan content bloğu içinde ama en sona koyduk -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        const personCheckboxes = document.querySelectorAll('.person-checkbox');
        const deleteSelectedBtn = document.getElementById('delete-selected-btn');
        const personForm = document.getElementById('person-form');

        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            personCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            updateDeleteButtonState();
        });

        personCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateDeleteButtonState);
        });

        deleteSelectedBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
            modal.show();
        });

        function updateDeleteButtonState() {
            const checkedBoxes = document.querySelectorAll('.person-checkbox:checked');
            deleteSelectedBtn.disabled = checkedBoxes.length === 0;

            selectAllCheckbox.checked = personCheckboxes.length > 0 &&
                                        checkedBoxes.length === personCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedBoxes.length > 0 &&
                                             checkedBoxes.length < personCheckboxes.length;
        }
    });
</script>
</div>
{% endblock %}