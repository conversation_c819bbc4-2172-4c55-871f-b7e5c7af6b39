from django.db import models
from django.conf import settings

# Create your models here.

class Camera(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='cameras')
    name = models.CharField(max_length=100)
    rtsp_url = models.CharField(max_length=255)
    camera_type = models.CharField(max_length=50)
    is_streaming = models.BooleanField(default=False)  # New field to track streaming status
    last_seen = models.DateTimeField(null=True, blank=True)
    frame_drop_rate = models.FloatField(default=0.0)
    latency = models.FloatField(default=0.0)
    fps = models.FloatField(default=0.0)
    RECOGNIZER_CHOICES = [
        ('arcface', 'ArcFaceRecognizer'),
        ('facenet', 'FaceNetRecognizer'),
    ]
    recognizer = models.CharField(
        max_length=10,
        choices=RECOGNIZER_CHOICES,
        default='arcface',
    )

    def is_healthy(self):
        # Basic health check: is the camera streaming?
        return self.is_streaming

    # Add any other fields or methods as needed

    def __str__(self):
        return self.name

class Guest(models.Model):
    flat = models.ForeignKey('Flat', on_delete=models.CASCADE, related_name='guests')
    name = models.CharField(max_length=100)
    photo = models.ImageField(upload_to='guest_photos/')
    entry_date_time = models.DateTimeField(null=True, blank=True)
    exit_date_time = models.DateTimeField(null=True, blank=True)
    recurring = models.BooleanField(default=False)
    recurring_days = models.CharField(max_length=100, blank=True, null=True, help_text="Days of the week for recurring visits (e.g., Mon,Wed,Fri)")
    access_duration = models.DurationField(null=True, blank=True, help_text="Maximum duration for guest access")

    def __str__(self):
        return self.name

class Flat(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='flats')
    name = models.CharField(max_length=100)

    def __str__(self):
        return self.name
