from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from recognition.arcface_recognizer import ArcFaceR<PERSON>ognizer
from recognition.facenet_recognizer import Face<PERSON>R<PERSON>ognizer
from .models import AlertPhoto, AlertPerson, Alarm, PersonTimeLog
from .forms import AlertPhotoForm, AlertPersonForm, RenameUnknownPersonForm
from .utils import calculate_image_vector, detect_faces
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .serializers import AlertPersonSerializer, AlertPhotoSerializer, AlarmSerializer
import numpy as np
from PIL import Image
from django.core.files.base import ContentFile
from io import BytesIO
import django_filters
from django.contrib import messages
from rest_framework.permissions import IsAuthenticated
from django.db.models import Count
import os
import shutil
from django.conf import settings
import logging
from datetime import datetime, date
from django.db.models import Q

logger = logging.getLogger(__name__)

@login_required
def index(request):
    total_persons = AlertPerson.objects.filter(user=request.user).count()
    return render(request, 'alerts/index.html', {'total_persons': total_persons})

def alert_photo_list(request):
    photos = AlertPhoto.objects.all().order_by('-created_at')
    paginator = Paginator(photos, 10)  # 10 photos per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    return render(request, 'alerts/photo_list.html', {'page_obj': page_obj})

def alert_photo_detail(request, photo_id):
    photo = get_object_or_404(AlertPhoto, id=photo_id)
    return render(request, 'alerts/photo_detail.html', {'photo': photo})


@login_required
def delete_multiple_alerts(request):
    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'delete_all':
            # Tüm alarmları sil
            Alarm.objects.filter(person__user=request.user).delete()
            messages.success(request, 'All alerts deleted successfully.')

        elif action == 'delete_selected':
            selected_alerts = request.POST.getlist('selected_alerts')
            if selected_alerts:
                # Seçili alarmları sil
                Alarm.objects.filter(
                    id__in=selected_alerts,
                    person__user=request.user
                ).delete()
                messages.success(request, 'Selected alerts deleted successfully.')
            else:
                messages.warning(request, 'No alerts were selected.')

    return redirect('alerts:alert_list')


@login_required
def add_alert_photo(request):
    if request.method == 'POST':
        form = AlertPhotoForm(request.POST, request.FILES)
        if form.is_valid():
            person_id = form.cleaned_data['person']
            person = get_object_or_404(AlertPerson, id=person_id, user=request.user)
            photos = request.FILES.getlist('photos')
            for photo in photos:
                image = Image.open(photo)
                face_boxes = detect_faces(np.array(image))
                if not face_boxes:
                    messages.error(request, f'No face detected in {photo.name}')
                else:
                    face_box = face_boxes[0]
                    face_image = image.crop(face_box)
                    if face_image.size[0] < 100 or face_image.size[1] < 100:
                        messages.error(request, f'Face is too small in {photo.name}')
                    else:
                        alert_photo = AlertPhoto(person=person)
                        buffer = BytesIO()
                        face_image.save(buffer, format='JPEG')
                        alert_photo.photo.save(f"{person.name}_face.jpg", ContentFile(buffer.getvalue()))

                        # Önce fotoğrafı kaydet
                        alert_photo.save()

                        try:
                            # ArcFace vektörünü hesapla
                            image_vector = calculate_image_vector(alert_photo.photo.path)
                            if image_vector is not None:
                                # Vektörü bytes'a dönüştür
                                vector_bytes = np.array(image_vector, dtype=np.float32).tobytes()
                                alert_photo.image_vector_arcface = vector_bytes
                                alert_photo.save()
                                messages.success(request, f'Photo {photo.name} added successfully')
                            else:
                                messages.warning(request, f'Could not calculate face vector for {photo.name}')
                        except Exception as e:
                            messages.error(request, f'Error processing face vector: {str(e)}')

            return redirect('alerts:person_detail', person_id=person.id)
    else:
        form = AlertPhotoForm()
    return render(request, 'alerts/add_photo.html', {'form': form})


def delete_alert_user(request, user_id):
    alert_person = get_object_or_404(AlertPerson, id=user_id)
    if request.method == 'POST':
        from recognition.arcface_recognizer import ArcFaceRecognizer
        from recognition.facenet_recognizer import FaceNetRecognizer
        
        # Her iki modeli de başlat
        arcface_recognizer = ArcFaceRecognizer()
        facenet_recognizer = FaceNetRecognizer()
        
        person_name = alert_person.name
        
        try:
            # Kişiye ait tüm fotoğrafları sil
            photos = AlertPhoto.objects.filter(person=alert_person)
            for photo in photos:
                if photo.photo and os.path.isfile(photo.photo.path):
                    os.remove(photo.photo.path)
            
            # Kişiye ait model dosyalarını sil - Kişinin kendi klasörü
            facebank_path = settings.RECOGNITION_CONFIG['facebank_path']
            
            # Kişinin kendi klasörünü sil (fotoğraflar)
            person_dir = os.path.join(facebank_path, person_name)
            if os.path.exists(person_dir):
                logger.info(f"Deleting directory for person {person_name}: {person_dir}")
                shutil.rmtree(person_dir)
            
            # Bilinmeyen kişiyse, unknowns klasöründe de kontrol et
            if alert_person.is_unknown:
                unknown_dir = os.path.join(facebank_path, 'unknowns', person_name)
                if os.path.exists(unknown_dir):
                    logger.info(f"Deleting unknown directory for person {person_name}: {unknown_dir}")
                    shutil.rmtree(unknown_dir)
            
            # Model dosyalarından kaldır - Bu işlemi kişiyi silmeden önce yap
            logger.info(f"Updating model embeddings for person {person_name}")
            arcface_success = arcface_recognizer.update_person_embeddings(person_name)
            facenet_success = facenet_recognizer.update_person_embeddings(person_name)
            
            if not arcface_success:
                logger.warning(f"Failed to update ArcFace embeddings for {person_name}")
            if not facenet_success:
                logger.warning(f"Failed to update FaceNet embeddings for {person_name}")
            
            # Kişiyi veritabanından sil (alarmlar cascade ile silinecek)
            alert_person.delete()
            
            # Model dosyalarının temizlendiğini doğrula
            cleanup_verified = verify_and_cleanup_model_files(person_name, logger)
            if not cleanup_verified:
                messages.warning(request, f"Kişi {person_name} silindi ancak model dosyaları tamamen temizlenememiş olabilir.")
            else:
                messages.success(request, f"Kişi {person_name} ve model dosyaları başarıyla silindi.")
            
        except Exception as e:
            logger.error(f"Error deleting person {person_name}: {str(e)}")
            messages.error(request, f"Kişi silinirken bir hata oluştu: {str(e)}")
        
        return redirect('alerts:photo_list')
    return render(request, 'alerts/confirm_delete.html', {'alert_person': alert_person})


@login_required
def add_alert_person(request):
    if request.method == 'POST':
        form = AlertPersonForm(request.POST)
        if form.is_valid():
            alert_person = form.save(commit=False)
            alert_person.user = request.user  # Set the user to the currently logged-in user
            alert_person.save()
            messages.success(request, 'Alert person added successfully.')
            return redirect('alerts:index')  # Redirect to the index page
    else:
        form = AlertPersonForm()
    return render(request, 'alerts/add_person.html', {'form': form})


@login_required
def person_detail(request, person_id):
    person = get_object_or_404(AlertPerson, id=person_id, user=request.user)
    
    # Her iki modeli de başlat
    arcface_recognizer = ArcFaceRecognizer()
    facenet_recognizer = FaceNetRecognizer()

    if request.method == 'POST':
        form = AlertPhotoForm(request.POST, request.FILES)
        if form.is_valid():
            photos = request.FILES.getlist('photos')
            for photo in photos:
                image = Image.open(photo)
                face_boxes, faces = detect_faces(np.array(image))

                if len(face_boxes) == 0:
                    messages.error(request, f'No face detected in {photo.name}')
                else:
                    face_boxes = face_boxes[:, :-1]
                    face_boxes = face_boxes.astype(int)
                    face_boxes = face_boxes + [-1, -1, 1, 1]
                    face_box = face_boxes[0]
                    face_width = face_box[2] - face_box[0]
                    face_height = face_box[3] - face_box[1]
                    if face_width < 100 or face_height < 100:
                        messages.error(request, f'Face is too small in {photo.name}')
                        form = AlertPhotoForm()
                        return render(request, 'alerts/add_photo.html', {'person': person, 'form': form})
                    else:
                        alert_photo = AlertPhoto(person=person)
                        buffer = BytesIO()
                        image.save(buffer, format='JPEG')
                        alert_photo.photo.save(f"{person.name}_face.jpg", ContentFile(buffer.getvalue()))

                        # Önce fotoğrafı kaydet ki path'i olsun
                        alert_photo.save()

                        # Her iki modelin facebankına ekle
                        arcface_success = arcface_recognizer.add_face(person.name, alert_photo.photo.path)
                        facenet_success = facenet_recognizer.add_face(person.name, alert_photo.photo.path)
                        
                        # Her iki model için embeddingler oluştur ve kaydet
                        try:
                            # ArcFace embeddingi hesapla
                            from alerts.utils import calculate_image_vector
                            arcface_vector = calculate_image_vector(alert_photo.photo.path, 'arcface')
                            if arcface_vector is not None:
                                alert_photo.image_vector_arcface = np.array(arcface_vector, dtype=np.float32).tobytes()
                                
                            # FaceNet embeddingi hesapla
                            facenet_vector = calculate_image_vector(alert_photo.photo.path, 'facenet')
                            if facenet_vector is not None:
                                alert_photo.image_vector_facenet = np.array(facenet_vector, dtype=np.float32).tobytes()
                                
                            # Güncellenmiş embedding'lerle kaydet
                            alert_photo.save()
                        except Exception as e:
                            logger.error(f"Error generating face embeddings: {str(e)}")

                        if arcface_success and facenet_success:
                            messages.success(request, f'Photo {photo.name} added successfully and added to both facebanks')
                        elif arcface_success:
                            messages.warning(request, f'Photo added to ArcFace facebank only, FaceNet failed: {photo.name}')
                        elif facenet_success:
                            messages.warning(request, f'Photo added to FaceNet facebank only, ArcFace failed: {photo.name}')
                        else:
                            messages.warning(request, f'Photo added but could not add to any facebank: {photo.name}')

            return redirect('alerts:person_detail', person_id=person.id)
    else:
        form = AlertPhotoForm()

    return render(request, 'alerts/add_photo.html', {'person': person, 'form': form})

@login_required
def alert_person_list(request):
    persons = AlertPerson.objects.filter(user=request.user).annotate(
        photo_count=Count('photos'),
        alarm_count=Count('alarm')
    )
    return render(request, 'alerts/person_list.html', {'persons': persons})

@login_required
def person_times(request, person_id):
    person = get_object_or_404(AlertPerson, id=person_id, user=request.user)

    # Tarih parametresini al, yoksa bugünü kullan
    selected_date_str = request.GET.get('date')
    if selected_date_str:
        try:
            selected_date = datetime.strptime(selected_date_str, '%Y-%m-%d').date()
        except ValueError:
            selected_date = date.today()
    else:
        selected_date = date.today()

    # Seçilen tarihteki time log'ları al
    time_logs = PersonTimeLog.objects.filter(
        person=person,
        timestamp__date=selected_date
    ).order_by('camera', 'timestamp')

    # Kamera bazında verileri grupla
    camera_summary = {}
    has_data = False

    for log in time_logs:
        camera_name = log.camera.name
        if camera_name not in camera_summary:
            camera_summary[camera_name] = {
                'entries': [],
                'exits': [],
                'entry_exit_pairs': [],
                'total_events': 0
            }

        if log.event_type == 'entry':
            camera_summary[camera_name]['entries'].append(log)
        else:  # exit
            camera_summary[camera_name]['exits'].append(log)

        camera_summary[camera_name]['total_events'] += 1
        has_data = True

    # Her kamera için giriş-çıkış çiftlerini oluştur
    for camera_name, data in camera_summary.items():
        entries = data['entries']
        exits = data['exits']
        pairs = []

        for i, entry in enumerate(entries):
            matching_exit = None
            for exit in exits:
                if exit.timestamp > entry.timestamp:
                    has_later_entry = any(
                        e.timestamp > entry.timestamp and e.timestamp < exit.timestamp
                        for e in entries
                    )
                    if not has_later_entry:
                        matching_exit = exit
                        break

            duration = None
            hours = None
            minutes = None
            if matching_exit:
                duration = matching_exit.timestamp - entry.timestamp
                total_seconds = int(duration.total_seconds())
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60

            pairs.append({
                'entry': entry,
                'exit': matching_exit,
                'duration': duration,
                'hours': hours,
                'minutes': minutes,
            })

        unmatched_exits = []
        for exit in exits:
            is_matched = any(pair['exit'] and pair['exit'].id == exit.id for pair in pairs)
            if not is_matched:
                unmatched_exits.append(exit)

        for exit in unmatched_exits:
            pairs.append({
                'entry': None,
                'exit': exit,
                'duration': None,
                'hours': None,
                'minutes': None,
            })

        pairs.sort(key=lambda x: (x['entry'].timestamp if x['entry'] else x['exit'].timestamp))
        data['entry_exit_pairs'] = pairs

    context = {
        'person': person,
        'selected_date': selected_date,
        'camera_summary': camera_summary,
        'has_data': has_data,
    }

    return render(request, 'alerts/person_times.html', context)

class AlertPersonViewSet(viewsets.ModelViewSet):
    serializer_class = AlertPersonSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['name']
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Return objects for the currently authenticated user only
        return AlertPerson.objects.filter(user=self.request.user)

    @action(detail=True, methods=['post'])
    def add_photo(self, request, pk=None):
        person = self.get_object()
        serializer = AlertPhotoSerializer(data=request.data)
        if serializer.is_valid():
            photo = serializer.save(person=person)
            image_vector = calculate_image_vector(photo.photo.path)
            photo.image_vector = image_vector
            photo.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class AlarmViewSet(viewsets.ModelViewSet):
    queryset = Alarm.objects.all()
    serializer_class = AlarmSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['person__name', 'camera__name', 'date']

    @action(detail=False, methods=['post'])
    def cleanup(self, request):
        # Implement synchronous cleanup logic here
        return Response(status=status.HTTP_202_ACCEPTED)

@login_required
def alert_list(request):
    alerts = Alarm.objects.filter(person__user=request.user).order_by('-date')
    paginator = Paginator(alerts, 10)  # 10 alerts per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    return render(request, 'alerts/alert_list.html', {'page_obj': page_obj})

@login_required
def delete_alert(request, alert_id):
    alert = get_object_or_404(Alarm, id=alert_id, person__user=request.user)
    if request.method == 'POST':
        alert.delete()
        messages.success(request, 'Alert deleted successfully.')
    return redirect('alerts:alert_list')

@login_required
def unknown_persons_list(request):
    """View to list all unknown persons"""
    unknown_persons = AlertPerson.objects.filter(
        user=request.user,
        is_unknown=True
    ).order_by('-last_seen_date')

    paginator = Paginator(unknown_persons, 10)  # Show 10 unknown persons per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'alerts/unknown_persons_list.html', {
        'page_obj': page_obj,
    })

@login_required
def unknown_person_detail(request, person_id):
    """View to show details of an unknown person and their photos"""
    person = get_object_or_404(AlertPerson, id=person_id, user=request.user, is_unknown=True)
    photos = AlertPhoto.objects.filter(person=person).order_by('-created_at')
    
    # Initialize the form for renaming
    form = RenameUnknownPersonForm()
    
    paginator = Paginator(photos, 5)  # Show 5 photos per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    return render(request, 'alerts/unknown_person_detail.html', {
        'person': person,
        'page_obj': page_obj,
        'form': form
    })

@login_required
def rename_unknown_person(request, person_id):
    """View to rename an unknown person"""
    person = get_object_or_404(AlertPerson, id=person_id, user=request.user, is_unknown=True)
    
    if request.method == 'POST':
        form = RenameUnknownPersonForm(request.POST)
        if form.is_valid():
            new_name = form.cleaned_data['new_name']
            old_name = person.name
            
            # Update the filesystem folders
            success = rename_person_folder(old_name, new_name)
            
            if success:
                # Önce yeni kişiyi oluştur
                new_person = AlertPerson.objects.create(
                    user=person.user,
                    name=new_name,
                    is_unknown=False,
                    first_seen_camera=person.first_seen_camera,
                    last_seen_date=person.last_seen_date,
                    created_at=person.created_at,
                    updated_at=person.updated_at
                )
                
                # Fotoğrafları yeni kişiye taşı
                AlertPhoto.objects.filter(person=person).update(person=new_person)
                
                # Alarmları yeni kişiye taşı
                Alarm.objects.filter(person=person).update(person=new_person)
                
                # Fotoğraf yollarını güncelle
                update_photo_paths(new_person, old_name, new_name)
                
                # Eski Unknown kişiyi sil
                person.delete()
                
                # Update only this person's embeddings instead of the entire facebank
                update_person_embeddings(new_name)
                
                messages.success(request, f'Person renamed from {old_name} to {new_name} successfully.')
                return redirect('alerts:person_list')
            else:
                messages.error(request, 'Failed to rename the folder. Please try again.')
    else:
        form = RenameUnknownPersonForm()
    
    photos = AlertPhoto.objects.filter(person=person).order_by('-created_at')
    paginator = Paginator(photos, 5)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    return render(request, 'alerts/unknown_person_detail.html', {
        'person': person,
        'page_obj': page_obj,
        'form': form
    })

def rename_person_folder(old_name, new_name):
    """Rename person folder and move from unknowns to regular folder"""
    try:
        # Paths
        base_dir = settings.MEDIA_ROOT
        unknown_dir = os.path.join(base_dir, 'alert_photos', 'unknowns')
        known_dir = os.path.join(base_dir, 'alert_photos')
        
        # Source and destination paths
        src_path = os.path.join(unknown_dir, old_name)
        dst_path = os.path.join(known_dir, new_name)
        
        # Check if source directory exists
        if not os.path.exists(src_path):
            logger.error(f"Source directory {src_path} does not exist")
            return False
        
        # Ensure destination directory doesn't exist
        if os.path.exists(dst_path):
            logger.error(f"Destination directory {dst_path} already exists")
            return False
        
        # Create destination directory
        os.makedirs(dst_path, exist_ok=True)
        
        # Move all files from source to destination
        for filename in os.listdir(src_path):
            src_file = os.path.join(src_path, filename)
            dst_file = os.path.join(dst_path, filename)
            shutil.copy2(src_file, dst_file)
        
        # Remove the source directory after successful copy
        shutil.rmtree(src_path)
        
        return True
    except Exception as e:
        logger.error(f"Error in rename_person_folder: {str(e)}")
        return False

def update_person_embeddings(person_name):
    """Update only the embeddings for a specific person instead of rebuilding the entire facebank"""
    try:
        # Update ArcFace recognizer
        arcface = ArcFaceRecognizer()
        arcface.update_person_embeddings(person_name)
        
        # Update FaceNet recognizer
        facenet = FaceNetRecognizer()
        facenet.update_person_embeddings(person_name)
        
        return True
    except Exception as e:
        logger.error(f"Error updating person embeddings: {str(e)}")
        return False


def update_photo_paths(person, old_name, new_name):
    """Update photo paths in database after renaming a person"""
    try:
        photos = AlertPhoto.objects.filter(person=person)
        for photo in photos:
            if photo.photo:
                # Eski dosya yolu: media/alert_photos/unknowns/old_name/filename.jpg
                # Yeni dosya yolu: media/alert_photos/new_name/filename.jpg
                old_path = photo.photo.name
                
                # Dosya adını al (ör: example_face.jpg)
                filename = os.path.basename(old_path)
                
                # Yeni yolu oluştur
                new_path = os.path.join('alert_photos', new_name, filename)
                
                # Veritabanındaki photo alanını güncelle
                photo.photo.name = new_path
                photo.save()
                
        return True
    except Exception as e:
        logger.error(f"Error updating photo paths: {str(e)}")
        return False

@login_required
def delete_photo(request, photo_id):
    photo = get_object_or_404(AlertPhoto, id=photo_id)
    
    # Kullanıcının bu fotoğrafa erişim yetkisini kontrol et
    if photo.person.user != request.user:
        messages.error(request, 'You do not have permission to delete this photo.')
        return redirect('alerts:person_detail', person_id=photo.person.id)
    
    person_id = photo.person.id
    person_name = photo.person.name
    
    if request.method == 'POST':
        # Fotoğraf dosyasını silmek için
        if photo.photo:
            if os.path.isfile(photo.photo.path):
                os.remove(photo.photo.path)
                
        # Fotoğrafı veritabanından sil
        photo.delete()
        messages.success(request, 'Photo deleted successfully.')
        
        # Bu kişinin diğer fotoğrafları var mı kontrol et
        remaining_photos = AlertPhoto.objects.filter(person_id=person_id).exists()
        
        # Eğer primary foto sildiysen ve başka foto varsa, birini primary yap
        if not AlertPhoto.objects.filter(person_id=person_id, is_primary=True).exists() and remaining_photos:
            first_photo = AlertPhoto.objects.filter(person_id=person_id).first()
            if first_photo:
                first_photo.is_primary = True
                first_photo.save()
        
        # Her iki model için bu kişinin embeddings verilerini güncelle
        try:
            # ArcFace için
            from recognition.arcface_recognizer import ArcFaceRecognizer
            arcface_recognizer = ArcFaceRecognizer()
            
            # FaceNet için
            from recognition.facenet_recognizer import FaceNetRecognizer
            facenet_recognizer = FaceNetRecognizer()
            
            # Kişi için embeddings'leri güncelle
            arcface_recognizer.update_person_embeddings(person_name)
            facenet_recognizer.update_person_embeddings(person_name)
            
            logger.info(f"Updated embeddings for person {person_name} after photo deletion")
        except Exception as e:
            logger.error(f"Error updating embeddings for person {person_name}: {str(e)}")
        
        return redirect('alerts:person_detail', person_id=person_id)
    
    # GET isteği için onay sayfasını göster
    return render(request, 'alerts/confirm_photo_delete.html', {'photo': photo})

@login_required
def delete_selected_persons(request):
    if request.method == 'POST':
        selected_persons = request.POST.getlist('selected_persons')
        if selected_persons:
            from recognition.arcface_recognizer import ArcFaceRecognizer
            from recognition.facenet_recognizer import FaceNetRecognizer
            
            # Her iki modeli de başlat
            arcface_recognizer = ArcFaceRecognizer()
            facenet_recognizer = FaceNetRecognizer()
            
            deleted_persons = []
            
            for person_id in selected_persons:
                try:
                    # Kişiyi veritabanından al
                    person = get_object_or_404(AlertPerson, id=person_id, user=request.user)
                    person_name = person.name
                    
                    # Kişiye ait tüm fotoğrafları sil
                    photos = AlertPhoto.objects.filter(person=person)
                    for photo in photos:
                        if photo.photo and os.path.isfile(photo.photo.path):
                            os.remove(photo.photo.path)
                    
                    # Kişiye ait model dosyalarını sil - Kişinin kendi klasörü
                    facebank_path = settings.RECOGNITION_CONFIG['facebank_path']
                    
                    # Kişinin kendi klasörünü sil (fotoğraflar)
                    person_dir = os.path.join(facebank_path, person_name)
                    if os.path.exists(person_dir):
                        logger.info(f"Deleting directory for person {person_name}: {person_dir}")
                        shutil.rmtree(person_dir)
                    
                    # Bilinmeyen kişiyse, unknowns klasöründe de kontrol et
                    if person.is_unknown:
                        unknown_dir = os.path.join(facebank_path, 'unknowns', person_name)
                        if os.path.exists(unknown_dir):
                            logger.info(f"Deleting unknown directory for person {person_name}: {unknown_dir}")
                            shutil.rmtree(unknown_dir)
                    
                    # Model dosyalarından kaldır - Bu işlemi kişiyi silmeden önce yap
                    logger.info(f"Updating model embeddings for person {person_name}")
                    arcface_success = arcface_recognizer.update_person_embeddings(person_name)
                    facenet_success = facenet_recognizer.update_person_embeddings(person_name)
                    
                    if not arcface_success:
                        logger.warning(f"Failed to update ArcFace embeddings for {person_name}")
                    if not facenet_success:
                        logger.warning(f"Failed to update FaceNet embeddings for {person_name}")
                    
                    # Kişiyi veritabanından sil (alarmlar cascade ile silinecek)
                    person.delete()
                    
                    # Model dosyalarının temizlendiğini doğrula
                    cleanup_verified = verify_and_cleanup_model_files(person_name, logger)
                    if cleanup_verified:
                        deleted_persons.append(person_name)
                    else:
                        logger.warning(f"Model cleanup may be incomplete for person: {person_name}")
                        deleted_persons.append(f"{person_name} (model cleanup warning)")
                    
                except Exception as e:
                    logger.error(f"Error deleting person {person_id}: {str(e)}")
                    messages.error(request, f"Error deleting person: {str(e)}")
            
            # Başarılı mesajı
            if deleted_persons:
                logger.info(f"Successfully deleted persons: {', '.join(deleted_persons)}")
                messages.success(request, f"{len(deleted_persons)} kişi ve model dosyaları başarıyla silindi.")
        else:
            messages.warning(request, "No persons were selected.")
    
    return redirect('alerts:person_list')

def verify_and_cleanup_model_files(person_name, logger=None):
    """
    Helper function to verify and clean up model files for a deleted person
    Returns True if cleanup was successful, False otherwise
    """
    if not logger:
        import logging
        logger = logging.getLogger(__name__)
    
    try:
        from recognition.arcface_recognizer import ArcFaceRecognizer
        from recognition.facenet_recognizer import FaceNetRecognizer
        
        # Initialize recognizers
        arcface_recognizer = ArcFaceRecognizer()
        facenet_recognizer = FaceNetRecognizer()
        
        # Check if person still exists in model files and remove if necessary
        arcface_cleaned = False
        facenet_cleaned = False
        
        # Check ArcFace model files
        if arcface_recognizer.names is not None and person_name in arcface_recognizer.names:
            logger.warning(f"Person {person_name} still found in ArcFace model, attempting cleanup")
            arcface_cleaned = arcface_recognizer.update_person_embeddings(person_name)
        else:
            arcface_cleaned = True
            
        # Check FaceNet model files  
        if facenet_recognizer.names is not None and person_name in facenet_recognizer.names:
            logger.warning(f"Person {person_name} still found in FaceNet model, attempting cleanup")
            facenet_cleaned = facenet_recognizer.update_person_embeddings(person_name)
        else:
            facenet_cleaned = True
        
        if arcface_cleaned and facenet_cleaned:
            logger.info(f"Model cleanup verified successful for person: {person_name}")
            return True
        else:
            logger.error(f"Model cleanup failed for person: {person_name}")
            return False
            
    except Exception as e:
        logger.error(f"Error during model cleanup verification for {person_name}: {str(e)}")
        return False

@login_required
def cleanup_orphaned_embeddings(request):
    """
    Clean up orphaned embeddings in model files for persons that no longer exist in database
    """
    if request.method == 'POST':
        try:
            from recognition.arcface_recognizer import ArcFaceRecognizer
            from recognition.facenet_recognizer import FaceNetRecognizer
            from alerts.models import AlertPerson
            
            # Initialize recognizers
            arcface_recognizer = ArcFaceRecognizer()
            facenet_recognizer = FaceNetRecognizer()
            
            # Get all existing person names from database
            existing_persons = set(AlertPerson.objects.filter(user=request.user).values_list('name', flat=True))
            
            orphaned_arcface = []
            orphaned_facenet = []
            
            # Check ArcFace model for orphaned entries
            if arcface_recognizer.names is not None:
                for name in set(arcface_recognizer.names):
                    if name not in existing_persons and name != "Unknown":
                        orphaned_arcface.append(name)
            
            # Check FaceNet model for orphaned entries
            if facenet_recognizer.names is not None:
                for name in set(facenet_recognizer.names):
                    if name not in existing_persons and name != "Unknown":
                        orphaned_facenet.append(name)
            
            # Clean up orphaned entries
            cleaned_count = 0
            for name in orphaned_arcface:
                if arcface_recognizer.update_person_embeddings(name):
                    cleaned_count += 1
                    logger.info(f"Cleaned orphaned ArcFace embedding for: {name}")
            
            for name in orphaned_facenet:
                if facenet_recognizer.update_person_embeddings(name):
                    cleaned_count += 1
                    logger.info(f"Cleaned orphaned FaceNet embedding for: {name}")
            
            if cleaned_count > 0:
                messages.success(request, f"Cleaned up {cleaned_count} orphaned model entries.")
            else:
                messages.info(request, "No orphaned model entries found.")
                
        except Exception as e:
            logger.error(f"Error during model cleanup: {str(e)}")
            messages.error(request, f"Error during model cleanup: {str(e)}")
    
    return redirect('alerts:photo_list')
