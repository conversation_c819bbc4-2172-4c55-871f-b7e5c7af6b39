import logging
import traceback

import numpy as np
from django.http import StreamingHttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from cameras.models import Camera
from django.shortcuts import get_object_or_404

from django.conf import settings

logger = logging.getLogger(__name__)

from django.db import models
from cameras.models import Camera
import json
from videostream.models import Zone
from videostream.zone_utils import process_zones
from videostream.camera_manager import CameraManager


# Global camera manager
camera_manager = CameraManager()

def generate_frames(camera_id):
    """Generate MJPEG frames for a specific camera"""
    while True:
        frame = camera_manager.get_frame(camera_id)
        if frame is not None:
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')


@login_required
def stream_viewer(request):
    """Render stream viewer page"""
    try:
        camera_id = request.GET.get('camera_id')
        stream_url = request.GET.get('camera_url', '')
        logger.info(f"Received stream URL: {stream_url}")

        context = {
            'stream_url': stream_url,
            'camera_id': camera_id
        }
        return render(request, 'stream_viewer.html', context)
    except Exception as e:
        logger.error(f"Stream viewer error: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)
    

@login_required
def zone_creator(request):
    """Render zone creator page"""
    try:
        camera_id = request.GET.get('camera_id')
        stream_url = request.GET.get('camera_url', '')
        logger.info(f"Received stream URL: {stream_url}")

        context = {
            'stream_url': stream_url,
            'camera_id': camera_id
        }

        return render(request, 'zone_creator.html', context)
    except Exception as e:
        logger.error(f"Zone creator error: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@login_required
def zone_request_handler(request):
    """Handle zone requests for a specific camera"""
    try:
        data = json.loads(request.body)
        camera_id = data.get('camera_id')
        submitted_zones = data.get('zones', [])

        if not camera_id:
            return JsonResponse({'error': 'Camera ID is required'}, status=400)
        
        camera = get_object_or_404(Camera, id=camera_id, user=request.user)

        # Process zones
        response_data = process_zones(camera, submitted_zones)

        return JsonResponse(response_data)
    except Exception as e:
        logger.error(f"Error saving zones: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)



@csrf_exempt
@login_required
def start_stream(request):
    """Start RTSP stream"""
    try:
        import json
        data = json.loads(request.body)
        url = data.get('url')
        camera_id = data.get('camera_id')

        if not url:
            return JsonResponse({'error': 'No URL provided'}, status=400)
        
        if not camera_id:
            return JsonResponse({'error': 'Camera ID is required'}, status=400)

        # If camera_id is provided, get the camera object for face detection
        camera_obj = None
        try:
            camera_obj = get_object_or_404(Camera, id=camera_id, user=request.user)
            # Update the is_streaming status to True
            camera_obj.is_streaming = True
            camera_obj.save()
        except Exception as e:
            logger.error(f"Error getting camera object: {str(e)}")
            # Continue without face detection if camera object can't be found
            pass

        camera_manager.start_stream(camera_id, url, camera_obj)

        return JsonResponse({'status': 'success'})
    except Exception as e:
        logger.error(f"Error starting stream: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@login_required
def stop_stream(request):
    """Stop RTSP stream"""
    try:
        # Handle both regular requests and sendBeacon requests
        if request.content_type == 'application/json' or request.body:
            try:
                data = json.loads(request.body) if request.body else {}
            except json.JSONDecodeError:
                # Handle sendBeacon requests that might not be proper JSON
                data = {}
        else:
            data = {}

        camera_id = data.get('camera_id')
        
        if camera_id:
            # Update the is_streaming status to False
            try:
                camera = Camera.objects.get(id=camera_id)
                camera.is_streaming = False
                camera.save()
            except Exception as e:
                logger.error(f"Error updating camera status: {str(e)}")

            camera_manager.stop_stream(camera_id)
        else:
            # If stopping all streams, update all cameras
            for camera_id in camera_manager.cameras.keys():
                try:
                    camera = Camera.objects.get(id=camera_id)
                    camera.is_streaming = False
                    camera.save()
                except Exception as e:
                    logger.error(f"Error updating camera {camera_id} status: {str(e)}")

            camera_manager.stop_all_streams()
            
        return JsonResponse({'status': 'success'})
    except Exception as e:
        logger.error(f"Error stopping stream: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def mjpeg_stream(request):
    """Stream MJPEG frames"""
    camera_id = request.GET.get('camera_id')
    if not camera_id:
        return JsonResponse({'error': 'Camera ID is required'}, status=400)
        
    return StreamingHttpResponse(generate_frames(camera_id),
                                 content_type='multipart/x-mixed-replace; boundary=frame')

@login_required
def view_zones(request):
    """Render view zones page"""
    try:
        camera_id = request.GET.get('camera_id')
        stream_url = request.GET.get('camera_url', '')
        logger.info(f"Received stream URL: {stream_url}")

        context = {
            'stream_url': stream_url,
            'camera_id': camera_id
        }

        return render(request, 'view_zones.html', context)
    except Exception as e:
        logger.error(f"View zones error: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@login_required
def get_zones(request):
    """Fetch zones for a specific camera"""
    try:
        camera_id = request.GET.get('camera_id')
        camera = get_object_or_404(Camera, id=camera_id, user=request.user)
        zones = Zone.objects.filter(camera=camera)

        zones_data = [
            {'name': zone.name, 'points': zone.points, 'color': zone.color}
            for zone in zones
        ]

        return JsonResponse({'zones': zones_data})  # Return empty list if no zones
    except Exception as e:
        logger.error(f"Error fetching zones: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'zones': []})  # Return empty list on error

@csrf_exempt
@login_required
def toggle_zone_filter(request):
    """Toggle zone filtering on/off for a specific camera"""
    try:
        data = json.loads(request.body)
        camera_id = data.get('camera_id')
        filter_active = data.get('filter_active', False)
        
        if not camera_id:
            return JsonResponse({'error': 'Camera ID is required'}, status=400)
            
        # Get the RTSPCamera instance from the manager
        with camera_manager.lock:
            if camera_id not in camera_manager.cameras:
                return JsonResponse({'error': 'Camera not streaming'}, status=400)
                
            rtsp_camera = camera_manager.cameras[camera_id]
            
            # Update the RTSPCamera object to filter by zone
            rtsp_camera.filter_by_zone = filter_active
            
            # If zone filtering is active, load zones for the camera
            if filter_active:
                camera = get_object_or_404(Camera, id=camera_id, user=request.user)
                zones = Zone.objects.filter(camera=camera)
                rtsp_camera.zones = [
                    {'name': zone.name, 'points': zone.points, 'color': zone.color}
                    for zone in zones
                ]
            else:
                # Clear zones if filtering is turned off
                rtsp_camera.zones = []
            
        return JsonResponse({'status': 'success', 'filter_active': filter_active})
    except Exception as e:
        logger.error(f"Error toggling zone filter: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@login_required
def update_fps(request):
    """Update FPS value for a camera"""
    try:
        data = json.loads(request.body)
        camera_id = data.get('camera_id')
        fps = data.get('fps', 0)

        if not camera_id:
            return JsonResponse({'error': 'Camera ID is required'}, status=400)

        camera = get_object_or_404(Camera, id=camera_id, user=request.user)
        camera.fps = fps
        camera.save()

        return JsonResponse({'status': 'success'})
    except Exception as e:
        logger.error(f"Error updating FPS: {str(e)}\n{traceback.format_exc()}")
        return JsonResponse({'error': str(e)}, status=500)

