from django import forms
from .models import Camera

class CameraForm(forms.ModelForm):
    class Meta:
        model = Camera
        fields = ['name', 'rtsp_url', 'recognizer']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'rtsp_url': forms.TextInput(attrs={'class': 'form-control'}),
            'recognizer': forms.Select(attrs={'class': 'form-control'}),
        }
