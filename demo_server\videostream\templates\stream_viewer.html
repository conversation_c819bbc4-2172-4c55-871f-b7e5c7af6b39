
{% extends "base.html" %}
{% load static %}

{% block title %}Video Stream{% endblock %}

{% block content %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
<style>
    :root {
        --primary-color: #3a86ff;
        --dark-bg: #1a1a1a;
        --medium-bg: #2a2a2a;
        --light-bg: #333333;
        --text-light: #f8f9fa;
        --text-muted: #adb5bd;
        --danger: #e63946;
        --success: #2a9d8f;
    }
    * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--dark-bg);
            color: var(--text-light);
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            border-bottom: 1px solid var(--light-bg);
            margin-bottom: 20px;
        }
        
        h1 {
            font-size: 24px;
            font-weight: 500;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        @media (min-width: 992px) {
            .main-content {
                grid-template-columns: 3fr 1fr;
            }
        }
        
        .stream-container {
            background: var(--medium-bg);
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            aspect-ratio: 16/9;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .stream-img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        .stream-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            transition: opacity 0.3s;
            opacity: 0;
        }
        
        .stream-container:hover .stream-overlay {
            opacity: 1;
        }
        
        .controls-panel {
            background: var(--medium-bg);
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group:last-child {
            margin-bottom: 0;
        }
        
        .control-group h3 {
            margin-bottom: 10px;
            font-size: 16px;
            color: var(--text-muted);
            font-weight: 500;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
        }
        
        button {
            background-color: var(--light-bg);
            color: var(--text-light);
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            cursor: pointer;
            transition: background-color 0.2s, transform 0.1s;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        button:hover {
            background-color: var(--primary-color);
        }
        
        button:active {
            transform: translateY(1px);
        }
        
        button.start-btn {
            background-color: var(--success);
        }
        
        button.stop-btn {
            background-color: var(--danger);
        }
        
        #frame-counter {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0,0,0,0.6);
            color: var(--text-light);
            padding: 5px 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .status-panel {
            background: var(--medium-bg);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid var(--light-bg);
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        #status {
            margin-bottom: 10px;
            padding: 10px;
            background: var(--light-bg);
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        #debug-info {
            height: 100px;
            overflow: auto;
            padding: 10px;
            background: var(--light-bg);
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .loading-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255,255,255,0.1);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            display: none;
        }
        
        @keyframes spin {
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        .stream-img.loading + .loading-spinner {
            display: block;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.connected {
            background-color: var(--success);
            box-shadow: 0 0 8px var(--success);
        }
        
        .status-indicator.disconnected {
            background-color: var(--danger);
            box-shadow: 0 0 8px var(--danger);
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-video"></i> Video Stream</h1>
            <div class="connection-status">
                <span class="status-indicator disconnected" id="connection-indicator"></span>
                <span id="connection-text">Disconnected</span>
            </div>
        </header>
        
        <div class="main-content">
            <div>
                <div class="stream-container">
                    <img id="stream" class="stream-img" alt="Stream">
                    <div id="frame-counter"><i class="fas fa-tachometer-alt"></i> 0 FPS</div>
                    <div class="loading-spinner"></div>
                    <div class="stream-overlay">
                        <div class="stream-info">
                            <span id="stream-resolution"></span>
                        </div>
                    </div>
                </div>
                
                <div class="status-panel">
                    <h3>Stream Status</h3>
                    <div id="status">Ready to start streaming</div>
                </div>
            </div>
            
            <div class="controls-panel">
                <div class="control-group">
                    <h3>Stream Controls</h3>
                    <input type="hidden" id="stream-url" value="{{ stream_url|safe }}">
                    <input type="hidden" id="camera-id" value="{{ camera_id|default:'' }}">
                    <div class="button-group">
                        <button class="start-btn" onclick="startStream()">
                            <i class="fas fa-play"></i> Start
                        </button>
                        <button class="stop-btn" onclick="stopStream()">
                            <i class="fas fa-stop"></i> Stop
                        </button>
                    </div>
                </div>
                
                <div class="control-group">
                    <h3>Debug Information</h3>
                    <div id="debug-info">Debug information will appear here</div>
                </div>
            </div>
        </div>
    </div>

    <script src="{% static 'videostream/js/stream.js' %}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const streamImg = document.getElementById('stream');
            
            // Show loading state
            streamImg.addEventListener('loadstart', function() {
                streamImg.classList.add('loading');
            });
            
            streamImg.addEventListener('load', function() {
                streamImg.classList.remove('loading');
                document.getElementById('connection-indicator').classList.replace('disconnected', 'connected');
                document.getElementById('connection-text').textContent = 'Connected';
                
                // Update resolution information
                document.getElementById('stream-resolution').textContent = 
                    `${this.naturalWidth}x${this.naturalHeight}`;
            });
            
            streamImg.addEventListener('error', function() {
                document.getElementById('connection-indicator').classList.replace('connected', 'disconnected');
                document.getElementById('connection-text').textContent = 'Disconnected';
            });
            
            // Automatically start stream if URL is provided
            const streamUrl = document.getElementById('stream-url').value;
            if (streamUrl) {
                startStream();
            }
            
            // Zone filter activation
            setTimeout(function() {
                const cameraId = document.getElementById('camera-id').value;
                activateZoneFilter(cameraId);
            }, 1000);        });
    </script>
{% endblock %}