import os
from asyncio.log import logger

import cv2
import numpy as np
import torch
from django.conf import settings

from alerts.models import AlertPhoto
from .base_recognizer import BaseRecognizer
from .arcface_scrfd import SCRFD
from .arcface_onnx import ArcFaceONNX
import io

class ArcFaceRecognizer(BaseRecognizer):
    def __init__(self, detector=None, recognizer=None):
        """
        ArcFace tanıyıcı sınıfı - Dependency Injection yaklaşımı ile model bileşenlerini kabul eder
        Eğer model bileşenleri verilmezse kendisi yükler
        
        Args:
            detector: Önceden yüklenmiş bir SCRFD dedektör nesnesi
            recognizer: Önceden yüklenmiş bir ArcFaceONNX tanıyıcı nesnesi
        """
        logger.info("Initializing ArcFaceRecognizer with dependency injection approach")
        self.conf = settings.RECOGNITION_CONFIG
        self.conf['model_path'] = os.path.expanduser(self.conf['model_path'])
        
        # Model bileşenlerini yükle veya dı<PERSON><PERSON><PERSON><PERSON> al<PERSON>nan<PERSON> kullan
        self.detector = detector or self._load_detector()
        self.recognizer = recognizer or self._load_recognizer()
        
        self.embeddings = None
        self.names = None

        self._load_facebank()
        logger.info("ArcFaceRecognizer initialization complete")

    def _load_detector(self):
        """Yüz dedektörünü yükle"""
        logger.info("Yüz dedektörü (SCRFD) yükleniyor")
        det_model_path = os.path.join(self.conf['model_path'], 'buffalo_l/det_10g.onnx')
        detector = SCRFD(det_model_path)
        detector.prepare(0, use_cuda=True)  # Explicitly enable CUDA
        return detector
        
    def _load_recognizer(self):
        """Yüz tanıyıcıyı yükle"""
        logger.info("Yüz tanıyıcı (ArcFaceONNX) yükleniyor")
        rec_model_path = os.path.join(self.conf['model_path'], 'buffalo_l/w600k_r50.onnx')
        recognizer = ArcFaceONNX(rec_model_path)
        recognizer.prepare(0, use_cuda=True)  # Explicitly enable CUDA
        return recognizer

    def _load_facebank(self):
        """Load facebank and convert between dict and tensor formats as needed"""
        facebank_path = os.path.join(self.conf['facebank_path'], 'facebank_arcface.pth')
        names_path = os.path.join(self.conf['facebank_path'], 'names_arcface.npy')

        if os.path.exists(facebank_path) and os.path.exists(names_path):
            try:
                device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
                self.embeddings = torch.load(facebank_path, map_location=device, weights_only=True)
                self.names = np.load(names_path, allow_pickle=True)
            except Exception as e:
                print(f"Error loading facebank: {e}")
        else:
            self.prepare_facebank()

    def _save_facebank(self):
        """Save current facebank state"""
        torch.save(self.embeddings, os.path.join(self.conf['facebank_path'], 'facebank_arcface.pth'))
        np.save(os.path.join(self.conf['facebank_path'], 'names_arcface.npy'), self.names)

    def update_person_embeddings(self, person_name):
        """Update the embeddings only for a specific person"""
        if not person_name:
            logger.error("No person name provided for update_person_embeddings")
            return False
            
        # Find all indices where this person appears
        person_indices = []
        if self.names is not None:
            for i, name in enumerate(self.names):
                if name == person_name:
                    person_indices.append(i)
        
        # Determine the directory to scan based on whether it's an unknown person
        is_unknown = person_name.startswith("Unknown_")
        base_dir = self.conf['facebank_path']
        if is_unknown:
            person_dir = os.path.join(base_dir, 'unknowns', person_name)
        else:
            person_dir = os.path.join(base_dir, person_name)
        
        # If directory doesn't exist or can't be accessed, remove ALL instances of person from facebank
        if not os.path.exists(person_dir) or not os.access(person_dir, os.R_OK):
            if person_indices:
                # Remove ALL instances of the person from the facebank (reverse order to maintain indices)
                for idx in reversed(person_indices):
                    if idx == 0:
                        if len(self.names) > 1:
                            self.embeddings = self.embeddings[1:]
                            self.names = self.names[1:]
                        else:
                            self.embeddings = None
                            self.names = None
                            break
                    else:
                        self.embeddings = torch.cat([self.embeddings[:idx], self.embeddings[idx+1:]])
                        self.names = np.concatenate([self.names[:idx], self.names[idx+1:]])
                        
                logger.info(f"Removed {len(person_indices)} instances of {person_name} from ArcFace model")
            self._save_facebank()
            return True
        
        # Gather all face embeddings for this person
        embs = []
        for file in os.scandir(person_dir):
            if not file.is_file():
                continue
                
            try:
                img = cv2.imread(file.path)
                if img is None:
                    continue
                    
                bboxes, kpss = self.detector.autodetect(img, max_num=1)
                if bboxes.shape[0] == 0:
                    continue
                    
                feat = self.recognizer.get(img, kpss[0]).ravel()
                device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
                embs.append(torch.from_numpy(feat).to(device).unsqueeze(0))
            except Exception as e:
                logger.error(f"Error processing file {file.path}: {str(e)}")
                continue
        
        # If no embeddings were found, remove ALL instances of person from facebank
        if len(embs) == 0:
            if person_indices:
                # Remove ALL instances of the person from the facebank (reverse order to maintain indices)
                for idx in reversed(person_indices):
                    if idx == 0:
                        if len(self.names) > 1:
                            self.embeddings = self.embeddings[1:]
                            self.names = self.names[1:]
                        else:
                            self.embeddings = None
                            self.names = None
                            break
                    else:
                        self.embeddings = torch.cat([self.embeddings[:idx], self.embeddings[idx+1:]])
                        self.names = np.concatenate([self.names[:idx], self.names[idx+1:]])
                        
                logger.info(f"Removed {len(person_indices)} instances of {person_name} from ArcFace model (no embeddings found)")
            self._save_facebank()
            return True
        
        # Calculate the mean embedding
        embedding = torch.cat(embs).mean(0, keepdim=True)
        
        # Remove ALL existing instances and add one new instance
        if person_indices:
            # Remove ALL existing instances (reverse order to maintain indices)
            for idx in reversed(person_indices):
                if idx == 0:
                    if len(self.names) > 1:
                        self.embeddings = self.embeddings[1:]
                        self.names = self.names[1:]
                    else:
                        self.embeddings = None
                        self.names = None
                        break
                else:
                    self.embeddings = torch.cat([self.embeddings[:idx], self.embeddings[idx+1:]])
                    self.names = np.concatenate([self.names[:idx], self.names[idx+1:]])
        
        # Add the new single embedding for this person
        device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        if self.embeddings is None:
            self.embeddings = embedding
            self.names = np.array([person_name])
        else:
            self.embeddings = self.embeddings.to(device)
            self.embeddings = torch.cat([self.embeddings, embedding])
            self.names = np.append(self.names, person_name)
        
        logger.info(f"Updated embeddings for {person_name} - consolidated {len(person_indices)} instances into 1")
        
        # Save the updated facebank
        self._save_facebank()
        return True

    def prepare_facebank(self):
        embeddings = []
        names = ['Unknown']
        for path in os.scandir(self.conf['facebank_path']):
            if path.is_file():
                continue
            else:
                embs = []
                for file in os.scandir(path.path):
                    if not file.is_file():
                        continue
                    else:
                        try:
                            img = cv2.imread(file.path)
                        except Exception as e:
                            print(f"Error opening image {file.path}: {e}")
                            continue
                        bboxes1, kpss1 = self.detector.autodetect(img, max_num=1)
                        if bboxes1.shape[0] == 0:
                            continue
                        feat1 = self.recognizer.get(img, kpss1[0]).ravel()
                        embs.append(torch.from_numpy(feat1).unsqueeze(0))
            if len(embs) == 0:
                continue
            embedding = torch.cat(embs).mean(0, keepdim=True)
            embeddings.append(embedding)
            names.append(path.name)
            print(f'Added {path.name}')
        
        # Check if there are any embeddings to process
        if len(embeddings) > 0:
            self.embeddings = torch.cat(embeddings)
            self.names = np.array(names)
            torch.save(self.embeddings, os.path.join(self.conf['facebank_path'], 'facebank_arcface.pth'))
            np.save(os.path.join(self.conf['facebank_path'], 'names_arcface'), self.names)
        else:
            # Handle empty case
            device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu') 
            self.embeddings = None
            self.names = np.array([])
            torch.save(self.embeddings, os.path.join(self.conf['facebank_path'], 'facebank_arcface.pth'))
            np.save(os.path.join(self.conf['facebank_path'], 'names_arcface'), self.names)
            # print('No face embeddings found')

    def recognize(self, image, threshold):
        bboxes, kpss = self.detector.autodetect(image)
        if bboxes.shape[0] == 0:
            return bboxes, [], []

        if self.embeddings is None or self.embeddings.shape[0] == 0:
            return (bboxes[:, :-1].astype(int) + [-1, -1, 1, 1],
                    ["Unknown"] * bboxes.shape[0],
                    [0.0] * bboxes.shape[0])
        else:
            bboxes = bboxes[:, :-1]
            bboxes = bboxes.astype(int)
            bboxes = bboxes + [-1, -1, 1, 1]
            
            # Set device consistently
            device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
            
            feats = []
            for i in range(bboxes.shape[0]):
                feat = self.recognizer.get(image, kpss[i]).ravel()
                # Move tensor to the correct device
                feats.append(torch.from_numpy(feat).to(device).unsqueeze(0))
                
            feats_emb = torch.cat(feats)
            
            # Ensure embeddings are on the same device as feats
            embeddings = self.embeddings.to(device)
            
            # Rest of calculation on consistent device
            sim = torch.mm(feats_emb, embeddings.transpose(1,0))
            sim = sim / (torch.norm(feats_emb, dim=1, keepdim=True) * torch.norm(embeddings, dim=1))
            maximum, max_idx = torch.max(sim, dim=1)
            max_idx[maximum < threshold] = -1
            names = []
            for r in max_idx:
                # Güvenli bir şekilde indeksleme yap - sınırları kontrol et
                r_int = r.item()  # Tensor'dan integer'a dönüştür
                if r_int == -1:
                    names.append("Unknown")
                else:
                    # Sınırları kontrol et
                    names_index = r_int + 1
                    if 0 <= names_index < len(self.names):
                        names.append(self.names[names_index])
                    else:
                        # İndeks sınır dışıysa Unknown kullan
                        logger.warning(f"Index {names_index} out of bounds for self.names with length {len(self.names)}")
                        names.append("Unknown")
            return bboxes, names, maximum.cpu().numpy()

    def add_face(self, name, filepath):
        try:
            img = cv2.imread(filepath)
            bboxes, kpss = self.detector.autodetect(img)
            if bboxes.shape[0] == 0:
                return False

            feat = self.recognizer.get(img, kpss[0]).ravel()
            # Set device consistently
            device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
            emb = torch.from_numpy(feat).to(device).unsqueeze(0)

            # Update tensor and names
            if self.embeddings is None or len(self.embeddings) == 0:
                self.embeddings = emb
                self.names = np.array([name])
            else:
                # Ensure embeddings are on the same device as new emb
                self.embeddings = self.embeddings.to(device)
                self.embeddings = torch.cat([self.embeddings, emb])
                self.names = np.append(self.names, name)

            # Save updated facebank
            self._save_facebank()

            # AlertPhoto'a embedding'i kaydet
            buffer = io.BytesIO()
            torch.save(emb, buffer)
            buffer.seek(0)
            try:
                photo_instance = AlertPhoto.objects.get(photo=filepath)
                photo_instance.image_vector_arcface = buffer.getvalue()
                photo_instance.save()
            except AlertPhoto.DoesNotExist:
                logger.info(f"No AlertPhoto found for {filepath}, continuing without saving vector")

            return True
        except Exception as e:
            logger.error(f"Error in add_face: {str(e)}")
            return False

    def detect_faces(self, frame):
        bboxes, kpss = self.detector.autodetect(frame)
        return bboxes, kpss

    def load_tensor(self, data):
        buffer = io.BytesIO(data)
        tensor = torch.load(buffer)
        return tensor
