import asyncio
import cv2
import numpy as np
from av import VideoFrame
from typing import Optional
import urllib.request
from aiortc import VideoStreamTrack

class CameraStream(VideoStreamTrack):
    def __init__(self, source):
        super().__init__()
        self.source = source
        self.stream = urllib.request.urlopen(self.source)
        self.buffer_size = 1256768
        self.byte_stream = np.zeros(self.buffer_size, dtype=np.uint8)
        self.data_length = 0

    async def recv(self):
        frame = await self.get_frame()
        return frame

    async def get_frame(self) -> Optional[np.ndarray]:
        if not self.is_running or self.stream is None:
            return None

        try:
            marker = np.frombuffer(b'\xff\xd8', dtype=np.uint8)
            marker2 = np.frombuffer(b'\xff\xd9', dtype=np.uint8)

            while True:
                buffer_view = memoryview(self.byte_stream)[self.data_length:]
                bytes_read = await asyncio.to_thread(self.stream.readinto, buffer_view)
                if bytes_read is None or bytes_read == 0:
                    return None
                self.data_length += bytes_read

                original = self.byte_stream[:self.data_length - 1]
                shifted = self.byte_stream[1:self.data_length]
                start_indices = np.where((original == marker[0]) & (shifted == marker[1]))[0]
                end_indices = np.where((original == marker2[0]) & (shifted == marker2[1]))[0]

                if len(start_indices) > 0 and len(end_indices) > 0:
                    frames = []
                    for start in start_indices:
                        end = end_indices[end_indices > start]
                        if len(end) > 0:
                            end = end[0]
                            frame_data = self.byte_stream[start:end + 2]
                            frame = cv2.imdecode(frame_data, cv2.IMREAD_COLOR)
                            if frame is not None:
                                frames.append(frame)

                    if frames:
                        # Move remaining data to the beginning of the buffer
                        last_end = end_indices[-1]
                        remaining = self.data_length - (last_end + 2)
                        self.byte_stream[:remaining] = self.byte_stream[last_end + 2:self.data_length]
                        self.data_length = remaining

                        # Return the last (most recent) frame
                        return frames[-1]

        except Exception as e:
            print(f"Error reading frame: {e}")
            return None