import torch
import numpy as np


npy_path = '../media/alert_photos/names.npy'
data = np.load(npy_path, allow_pickle=True)
print("Numpy dosyası yüklendi. Shape:", data.shape)
print("Numpy dosyası içeriği:", data)

facebank_path = '../media/alert_photos/facebank.pth'
facebank = torch.load(facebank_path, map_location=torch.device('cpu'))
print("Bir Tensor yüklendi. Shape:", facebank.shape)

print("Facebank içeriği:")
for name, tensor in facebank.items():
    print(name, tensor.shape)
