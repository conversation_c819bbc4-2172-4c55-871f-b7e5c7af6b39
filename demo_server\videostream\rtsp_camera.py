import threading
import cv2
import asyncio
import numpy as np
from videostream.face_recognition import FaceRecognitionProcessor
import logging
import traceback
import time
from queue import Queue, Empty

logger = logging.getLogger(__name__)

class RTSPCamera:
    def __init__(self):
        self.stream = None
        self.frame = None
        self.lock = threading.Lock()
        self.streaming = False
        self.face_processor = None
        self.camera_obj = None
        self.loop = asyncio.new_event_loop()
        self.filter_by_zone = False
        self.zones = []
        self.frame_queue = Queue(maxsize=15)  # Reduced frame queue size
        self.camera_id = None
        self.connection_error = None
        self.stream_thread = None
        self.process_thread = None
        self._shutdown_event = threading.Event()
        
    def start_stream(self, url, camera_obj=None):
        """Start RTSP stream with given URL"""
        if self.streaming:
            self.stop_stream()  # Stop existing stream if any
        
        self.connection_error = None
        self.camera_obj = camera_obj
        if camera_obj:
            self.camera_id = camera_obj.id
            # Initialize face processor with camera ID for proper recognizer selection
            # Singleton yapı kullanıldığından hep a<PERSON><PERSON>, sadece kamera ID'si güncellenir
            logger.info(f"Getting FaceRecognitionProcessor for camera ID: {self.camera_id}")
            self.face_processor = FaceRecognitionProcessor(camera_id=self.camera_id)
            logger.info(f"Face processor successfully initialized for camera ID: {self.camera_id}")
        else:
            self.face_processor = None  # No face recognition without camera object
            logger.info("No camera object provided, face recognition disabled")

        logger.info(f"Starting RTSP stream from URL: {url}")
        self.streaming = True
        self._shutdown_event.clear()

        # Start threads and keep references
        self.stream_thread = threading.Thread(target=self._stream_thread, args=(url,), daemon=False)
        self.process_thread = threading.Thread(target=self._process_frames, daemon=False)

        self.stream_thread.start()
        self.process_thread.start()

    def _stream_thread(self, url):
        """Thread for streaming video"""
        try:
            # Use a timeout for the VideoCapture open operation
            self.stream = cv2.VideoCapture(url)

            # Check if we can read frames
            ret, test_frame = self.stream.read()
            if not ret or test_frame is None:
                logger.error(f"Connected to stream but couldn't read frames: {url}")
                self.connection_error = "Connected to camera but couldn't read video frames"
                self.stop_stream()
                return
                
            # If we get here, connection is successful
            frames_count = 0
            consecutive_failures = 0
            max_failures = 30  # Allow up to 30 consecutive failures
            
            while self.streaming and not self._shutdown_event.is_set():
                ret, frame = self.stream.read()
                if ret and isinstance(frame, np.ndarray):
                    consecutive_failures = 0
                    frames_count += 1
                    try:
                        self.frame_queue.put_nowait(frame)
                    except:
                        pass  # Skip if queue is full
                else:
                    consecutive_failures += 1
                    logger.warning(f"Failed to read frame. Consecutive failures: {consecutive_failures}")
                    if consecutive_failures >= max_failures:
                        logger.error(f"Too many consecutive frame read failures: {max_failures}")
                        self.connection_error = "Lost connection to camera"
                        break
                    # Short sleep to avoid tight loop when failing
                    time.sleep(0.1)

                # Check for shutdown signal periodically
                if self._shutdown_event.wait(0.001):  # Non-blocking check
                    break
            
            logger.info(f"Stream stopped after processing {frames_count} frames")
        except Exception as e:
            logger.error(f"Stream thread error: {str(e)}")
            logger.error(traceback.format_exc())
            self.connection_error = f"Stream error: {str(e)}"
        finally:
            # Clean up stream resources without calling stop_stream to avoid recursion
            if self.stream is not None:
                try:
                    self.stream.release()
                    logger.info("Video stream released in _stream_thread")
                except Exception as e:
                    logger.error(f"Error releasing video stream in _stream_thread: {e}")
            self.streaming = False

    def _process_frames(self):
        """Process frames with model and update self.frame"""
        asyncio.set_event_loop(self.loop)

        while self.streaming and not self._shutdown_event.is_set():
            try:
                frame = self.frame_queue.get(timeout=1)
                processed_frame = frame.copy()

                # Check shutdown event before processing
                if self._shutdown_event.is_set():
                    break

                # Reduced frame skipping threshold
                if self.frame_queue.qsize() > 3:  # Reduced from 5 to 3
                    continue
                    
                if self.face_processor and self.camera_obj:
                    try:
                        # Process frame synchronously to ensure face detection works
                        processed_frame = self.loop.run_until_complete(
                            self.face_processor.process_frame(
                                frame,
                                self.camera_obj,
                                filter_by_zone=self.filter_by_zone,
                                zones=self.zones
                            )
                        )
                        with self.lock:
                            self.frame = processed_frame
                    except Exception as e:
                        logger.error(f"Face recognition error: {str(e)}")
                        logger.error(traceback.format_exc())
                        with self.lock:
                            self.frame = frame
                else:
                    with self.lock:
                        self.frame = frame

                # Reduced queue clearing threshold
                if self.frame_queue.qsize() > 5:  # Reduced from 10 to 5
                    while not self.frame_queue.empty():
                        try:
                            self.frame_queue.get_nowait()
                        except Empty:
                            break

            except Empty:
                continue
            except Exception as e:
                logger.error(f"Frame processing error: {str(e)}")
                logger.error(traceback.format_exc())

    def get_frame(self):
        """Get the current frame"""
        if self.frame is not None and isinstance(self.frame, np.ndarray):
            with self.lock:
                try:
                    ret, jpeg = cv2.imencode('.jpg', self.frame)
                    if ret:
                        return jpeg.tobytes()
                except Exception as e:
                    logger.error(f"Error encoding frame: {str(e)}")
        return None

    def stop_stream(self):
        """Stop the stream gracefully"""
        logger.info(f"Stopping stream for camera ID: {self.camera_id}")

        # Signal threads to stop
        self.streaming = False
        self._shutdown_event.set()

        # Get current thread to avoid self-join
        current_thread = threading.current_thread()

        # Only wait for threads if we're not being called from within them
        if (self.stream_thread and
            self.stream_thread.is_alive() and
            self.stream_thread != current_thread):
            logger.info("Waiting for stream thread to finish...")
            try:
                self.stream_thread.join(timeout=2.0)  # Reduced timeout
                if self.stream_thread.is_alive():
                    logger.warning("Stream thread did not finish gracefully")
            except Exception as e:
                logger.error(f"Error joining stream thread: {e}")

        if (self.process_thread and
            self.process_thread.is_alive() and
            self.process_thread != current_thread):
            logger.info("Waiting for process thread to finish...")
            try:
                self.process_thread.join(timeout=2.0)  # Reduced timeout
                if self.process_thread.is_alive():
                    logger.warning("Process thread did not finish gracefully")
            except Exception as e:
                logger.error(f"Error joining process thread: {e}")

        # Clean up resources
        if self.stream is not None:
            try:
                self.stream.release()
                logger.info("Video stream released successfully")
            except Exception as e:
                logger.error(f"Error releasing video stream: {e}")

        # Clear all references
        self.stream = None
        self.frame = None

        # Clear frame queue
        while not self.frame_queue.empty():
            try:
                self.frame_queue.get_nowait()
            except:
                break

        # Face processor artık Singleton olduğundan gerçekten silinmez, sadece referansı kaldırılır
        logger.info(f"Clearing face processor reference for camera ID: {self.camera_id}")
        self.face_processor = None

        # Clear thread references only after they're done
        if current_thread != self.stream_thread:
            self.stream_thread = None
        if current_thread != self.process_thread:
            self.process_thread = None

        logger.info(f"Stream stopped successfully for camera ID: {self.camera_id}")