from django.apps import AppConfig
import signal
import sys
import logging

logger = logging.getLogger(__name__)


class VideostreamConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "videostream"

    def ready(self):
        """Called when Django starts up"""
        # Import here to avoid circular imports
        from .camera_manager import camera_manager

        def signal_handler(signum, frame):
            """Handle shutdown signals gracefully"""
            logger.info(f"Received signal {signum}, shutting down gracefully...")
            try:
                camera_manager.stop_all_streams()
                logger.info("All camera streams stopped successfully")
            except Exception as e:
                logger.error(f"Error stopping camera streams: {e}")

            # Exit gracefully
            sys.exit(0)

        # Register signal handlers
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)

        logger.info("Signal handlers registered for graceful shutdown")