import numpy as np
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.conf import settings
import os
from django.utils.crypto import get_random_string


def unique_file_path(instance, filename):
    ext = filename.split('.')[-1]
    new_filename = f"{get_random_string(20)}.{ext}"

    # Çoklu kişi durumunda özel path
    if isinstance(instance, AlertPhoto) and instance.is_multiple_face:
        return os.path.join('alert_photos', 'multiple_faces', filename)

    # AlertPhoto için
    elif isinstance(instance, AlertPhoto):
        if instance.person.is_unknown:
            return os.path.join('alert_photos', 'unknowns', instance.person.name, new_filename)
        return os.path.join('alert_photos', instance.person.name, new_filename)

    # Alarm için
    elif isinstance(instance, Alarm):
        if instance.person.is_unknown:
            return os.path.join('alert_photos', 'unknowns', instance.person.name, 'alarms', new_filename)
        return os.path.join('alert_photos', instance.person.name, 'alarms', new_filename)

class Alert<PERSON>erson(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='alert_persons')
    name = models.CharField(max_length=100)
    is_unknown = models.BooleanField(default=False)
    first_seen_camera = models.ForeignKey('cameras.Camera', on_delete=models.SET_NULL, null=True)
    last_seen_date = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['name', 'is_unknown']),
        ]

    def __str__(self):
        return f"{self.name} ({'Unknown' if self.is_unknown else 'Known'})"

class AlertPhoto(models.Model):
    person = models.ForeignKey(AlertPerson, related_name='photos', on_delete=models.CASCADE, null=True, blank=True)
    photo = models.ImageField(upload_to=unique_file_path)
    image_vector_facenet = models.BinaryField(null=True, blank=True)  # Binary field for Facenet
    image_vector_arcface = models.BinaryField(null=True, blank=True)  # Binary field for ArcFace
    is_primary = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    is_multiple_face = models.BooleanField(default=False)

    class Meta:
        indexes = [
            models.Index(fields=['person', 'is_primary']),
        ]

    def save(self, *args, **kwargs):
        if self.is_primary:
            # Ensure only one primary photo per person
            AlertPhoto.objects.filter(person=self.person, is_primary=True).update(is_primary=False)

        # Convert vector to bytes if it's not already in bytes format
        if self.image_vector_arcface is not None and not isinstance(self.image_vector_arcface, (bytes, memoryview)):
            try:
                # Convert float array to bytes
                self.image_vector_arcface = np.array(self.image_vector_arcface, dtype=np.float32).tobytes()
            except Exception as e:
                print(f"Error converting vector to bytes: {e}")
                self.image_vector_arcface = None

        if self.image_vector_facenet is not None and not isinstance(self.image_vector_facenet, (bytes, memoryview)):
            try:
                self.image_vector_facenet = np.array(self.image_vector_facenet, dtype=np.float32).tobytes()
            except Exception as e:
                print(f"Error converting vector to bytes: {e}")
                self.image_vector_facenet = None

        super().save(*args, **kwargs)

    def get_arcface_vector(self):
        """Convert stored bytes back to numpy array"""
        if self.image_vector_arcface:
            try:
                return np.frombuffer(self.image_vector_arcface, dtype=np.float32)
            except:
                return None
        return None

    def get_facenet_vector(self):
        """Convert stored bytes back to numpy array"""
        if self.image_vector_facenet:
            try:
                return np.frombuffer(self.image_vector_facenet, dtype=np.float32)
            except:
                return None
        return None

class Alarm(models.Model):
    alert_photo = models.ForeignKey(AlertPhoto, on_delete=models.CASCADE, null=True, blank=True)
    person = models.ForeignKey(AlertPerson, on_delete=models.CASCADE)
    camera = models.ForeignKey('cameras.Camera', on_delete=models.CASCADE)
    date = models.DateTimeField(auto_now_add=True)
    video_snapshot = models.FileField(upload_to=unique_file_path, null=True, blank=True)
    confidence = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        default=0.0
    )

    class Meta:
        ordering = ['-date']
        indexes = [
            models.Index(fields=['person', '-date']),
        ]

    def __str__(self):
        return f"Alarm: {self.person.name} at {self.date:%Y-%m-%d %H:%M}"


class PersonTimeLog(models.Model):
    EVENT_TYPE_CHOICES = [
        ('entry', 'Entry'),
        ('exit', 'Exit'),
    ]

    person = models.ForeignKey(AlertPerson, on_delete=models.CASCADE, related_name='time_logs')
    camera = models.ForeignKey('cameras.Camera', on_delete=models.CASCADE)
    event_type = models.CharField(max_length=10, choices=EVENT_TYPE_CHOICES)
    timestamp = models.DateTimeField()
    confidence = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text='Recognition confidence score'
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['person', 'timestamp']),
            models.Index(fields=['camera', 'timestamp']),
            models.Index(fields=['person', 'camera', 'timestamp']),
        ]

    def __str__(self):
        return f"{self.person.name} - {self.get_event_type_display()} at {self.timestamp:%Y-%m-%d %H:%M}"