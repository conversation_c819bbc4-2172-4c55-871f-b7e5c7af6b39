{% extends 'base.html' %}

        {% block content %}
        <div class="camera-container">
            <h2 class="mb-4">Add Camera</h2>
            <form method="post" class="camera-form">
                {% csrf_token %}

                <div class="form-group mb-4">
                    <label for="id_name" class="form-label">Camera Name</label>
                    <input type="text" name="{{ form.name.name }}" id="id_name" class="form-control" {% if form.name.value %}value="{{ form.name.value }}"{% endif %}>
                </div>

                <div class="form-group mb-4">
                    <label for="id_rtsp_url" class="form-label">RTSP URL</label>
                    <input type="text" name="{{ form.rtsp_url.name }}" id="id_rtsp_url" class="form-control" {% if form.rtsp_url.value %}value="{{ form.rtsp_url.value }}"{% endif %}>
                    <small class="form-text text-muted">Enter the RTSP stream URL for your camera</small>
                </div>

                <div class="form-group mb-4">
                    <label for="id_recognizer" class="form-label">Face Recognition Model</label>
                    <div class="model-selection-container">
                        <select name="{{ form.recognizer.name }}" id="id_recognizer" class="form-select">
                            {% for value, text in form.recognizer.field.choices %}
                                <option value="{{ value }}" {% if value == form.recognizer.value %}selected{% endif %}>{{ text }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <small class="form-text text-muted">Select the model to use for face recognition with this camera</small>
                </div>

                <div class="form-buttons">
                    <button type="submit" class="btn btn-primary">Add Camera</button>
                    <a href="{% url 'cameras:index' %}" class="btn btn-outline-secondary ms-2">Cancel</a>
                </div>
            </form>
        </div>
        {% endblock %}

        {% block extra_js %}
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Additional JavaScript if needed
        });
        </script>
        {% endblock %}