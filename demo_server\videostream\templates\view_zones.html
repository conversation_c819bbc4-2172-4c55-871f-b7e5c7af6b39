
{% extends 'stream_viewer.html' %}
{% load static %}
{% block title %}Zone Viewer{% endblock %}
{% block header_title %}<i class="fas fa-eye"></i> Zone Viewer{% endblock %}
{% block controls_panel %}
<div class="control-group">
    <h3>Stream Controls</h3>
    <input type="hidden" id="stream-url" value="{{ stream_url|safe }}">
    <input type="hidden" id="camera-id" value="{{ camera_id|default:'' }}">
    <div class="button-group">
        <button class="start-btn" onclick="startStream()">
            <i class="fas fa-play"></i> Start
        </button>
        <button class="stop-btn" onclick="stopStream()">
            <i class="fas fa-stop"></i> Stop
        </button>
    </div>
</div>
<div class="control-group">
  <h3>Debug Information</h3>
  <div id="debug-info">Debug information will appear here</div>
</div>
<div class="control-group">
    <div style="display: flex; align-items: center; gap: 10px;">
        <span>Show Areas</span>
        <label class="switch">
            <input type="checkbox" id="toggle-areas" checked>
            <span class="slider"></span>
        </label>
    </div>
    <div class="areas">
        <h3>Areas</h3>
        <div id="area-list"></div>
    </div>
</div>
{% endblock %}
{% block extra_js %}
<script src="{% static 'videostream/js/canvas_manager.js' %}"></script>
<script src="{% static 'videostream/js/polygon.js' %}"></script>
<script src="{% static 'videostream/js/sidebar_manager.js' %}"></script>
<script src="{% static 'videostream/js/canvas_interaction_handler.js' %}"></script>
<script>
  window.onload = async function () {
    const canvas = document.getElementById("draw-canvas");
    const video = document.getElementById("stream");
    const areaList = document.getElementById("area-list");
    const toggleAreas = document.getElementById("toggle-areas");

    const isVisible = toggleAreas.checked;
    updateZoneFilter(isVisible);
    
    // Sayfa yüklendikten sonra otomatik olarak zone filtresini etkinleştir
    setTimeout(() => {
      // Önce toggle'ın durumunu kontrol et ve true olarak zorla
      if (!toggleAreas.checked) {
        toggleAreas.checked = true;
      }
      // Zone filtresini aktifleştir - API çağrısını garanti etmek için burada direkt true gönderiyoruz
      updateZoneFilter(true);
      console.log("Zone filter automatically activated on page load");
    }, 1000); // Diğer tüm yüklemeler tamamlandıktan sonra çalışması için kısa bir gecikme

    const canvasManager = new CanvasManager(canvas);
    let polygons = [];
          // Add a more robust approach to resize and position the canvas
    function updateCanvasPosition() {
        if (video.complete && video.naturalWidth !== 0) {
          canvasManager.resizeToMatch(video);
          canvasManager.draw();
        }
      }

      // Add event listeners to ensure canvas positioning on various events
      video.addEventListener('load', updateCanvasPosition);
      video.addEventListener('loadeddata', updateCanvasPosition);
      video.addEventListener('resize', updateCanvasPosition);
      
      // Initial positioning once video has loaded
      if (video.complete) {
        updateCanvasPosition();
      }
      
      // Check periodically for changes in video dimensions or position
      setInterval(updateCanvasPosition, 1000);

      // Allow time for the video to render before initial positioning
      setTimeout(updateCanvasPosition, 500);

    // Create a modified SidebarManager that doesn't show edit/delete buttons
    const sidebar = new SidebarManager(areaList, null, null);
    
    // Override the addPolygonLabel method to not display edit/delete buttons
    sidebar.addPolygonLabel = function(name, color, polygon) {
      const div = document.createElement('div');
      div.className = 'area-item';

      const labelDiv = document.createElement('div');
      labelDiv.className = 'area-label';

      const colorBox = document.createElement('div');
      colorBox.className = 'color-box';
      colorBox.style = `width: 16px; height: 16px; margin-right: 6px; display: inline-block; background: ${color}; border: 1px solid #999;`;

      const labelText = document.createElement('span');
      labelText.textContent = name;
      
      labelDiv.appendChild(colorBox);
      labelDiv.appendChild(labelText);
      div.appendChild(labelDiv);

      this.areaList.appendChild(div);
      polygon.element = div;
    };
    

    toggleAreas.addEventListener("change", () => {
      const isVisible = toggleAreas.checked;
      canvasManager.setPolygons(isVisible ? polygons : []);
      canvasManager.draw();
      
      // Send the zone filter state to the backend
      updateZoneFilter(isVisible);
    });
    
    // Function to toggle zone filtering in the backend
    async function updateZoneFilter(active) {
      const cameraId = document.getElementById("camera-id").value;
      if (!cameraId) {
        console.error("Camera ID is missing");
        return;
      }
      
      try {
        const response = await fetch('/videostream/toggle_zone_filter/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
          },
          body: JSON.stringify({
            camera_id: cameraId,
            filter_active: active
          })
        });
        
        const result = await response.json();
        if (!response.ok) {
          console.error(`Error toggling zone filter: ${result.error}`);
        }
      } catch (error) {
        console.error("Error updating zone filter:", error);
      }
    }
    
    // Function to get CSRF token
    function getCookie(name) {
      let cookieValue = null;
      if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i].trim();
          if (cookie.substring(0, name.length + 1) === (name + '=')) {
            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
            break;
          }
        }
      }
      return cookieValue;
    }

    async function fetchZones() {
      const cameraId = "{{ camera_id }}";
      try {
        const response = await fetch(`/videostream/get_zones/?camera_id=${cameraId}`);
        const result = await response.json();
        if (response.ok) {
          const videoWidth = video.clientWidth;
          const videoHeight = video.clientHeight;

          polygons = result.zones.map(zone => {
            const polygon = new Polygon(zone.color);
            polygon.name = zone.name;

            // Scale points back to video dimensions
            polygon.points = zone.points.map(point => ({
              x: point.x * videoWidth,
              y: point.y * videoHeight
            }));

            polygon.complete = true;
            sidebar.addPolygonLabel(zone.name, zone.color, polygon);
            return polygon;
          });

          canvasManager.setPolygons(polygons);
          canvasManager.draw();
          
          // Zone'lar yüklendikten sonra filtreleme durumunu tekrar kontrol et
          if (toggleAreas.checked) {
              updateZoneFilter(true);
              console.log("Zone filter reactivated after zones are loaded");
          }
        } else {
          console.error(`Error fetching zones: ${result.error}`);
        }
      } catch (error) {
        console.error("Error fetching zones:", error);
      }
    }

    // Stream başladıktan ve canvas hazır olduktan sonra zone'ları yükle
    setTimeout(() => {
        video.onload = () => {
          canvasManager.resizeToMatch(video);
          fetchZones();
        };
        if (video.complete) {
          canvasManager.resizeToMatch(video);
          fetchZones();
        }
    }, 2000);  // Gecikmeyi azalttım, sayfanın daha hızlı hazır olması için
    
    // Stream başladıktan bir süre sonra zone filtrelemesini yeniden kontrol et
    setTimeout(() => {
        if (toggleAreas.checked) {
            updateZoneFilter(true);
            console.log("Final zone filter check to ensure activation");
        }
    }, 5000);  // Stream başladıktan sonra son bir kontrol

  };
</script>
{% endblock %}