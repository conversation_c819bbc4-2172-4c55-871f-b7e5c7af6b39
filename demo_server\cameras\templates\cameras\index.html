{% extends 'base.html' %}

{% block content %}
<div class="container">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="fw-bold text-primary mb-1" data-aos="fade-right">
                        <i class="bi bi-camera-video-fill me-2"></i>Camera Management
                    </h2>
                    <p class="text-muted mb-0" data-aos="fade-right" data-aos-delay="100">
                        Manage and monitor your security cameras
                    </p>
                </div>
                <div data-aos="fade-left">
                    <a href="{% url 'cameras:add' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Add New Camera
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-3" data-aos="fade-up">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="bg-primary bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 48px; height: 48px;">
                        <i class="bi bi-camera-fill text-white"></i>
                    </div>
                    <h5 class="fw-bold mb-1">{{ cameras|length }}</h5>
                    <p class="text-muted mb-0 small">Total Cameras</p>
                </div>
            </div>
        </div>
        <div class="col-md-3" data-aos="fade-up" data-aos-delay="100">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="bg-success bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 48px; height: 48px;">
                        <i class="bi bi-play-circle-fill text-white"></i>
                    </div>
                    <h5 class="fw-bold mb-1">Active</h5>
                    <p class="text-muted mb-0 small">Streams</p>
                </div>
            </div>
        </div>
        <div class="col-md-3" data-aos="fade-up" data-aos-delay="200">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="bg-warning bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 48px; height: 48px;">
                        <i class="bi bi-cpu-fill text-white"></i>
                    </div>
                    <h5 class="fw-bold mb-1">AI</h5>
                    <p class="text-muted mb-0 small">Recognition</p>
                </div>
            </div>
        </div>
        <div class="col-md-3" data-aos="fade-up" data-aos-delay="300">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="bg-info bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 48px; height: 48px;">
                        <i class="bi bi-shield-check-fill text-white"></i>
                    </div>
                    <h5 class="fw-bold mb-1">24/7</h5>
                    <p class="text-muted mb-0 small">Monitoring</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Cameras Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm" data-aos="fade-up" data-aos-delay="400">
                <div class="card-header bg-gradient text-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul me-2"></i>Camera List
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if cameras %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th class="border-0 px-4 py-3">
                                            <i class="bi bi-camera-fill me-1"></i>Camera
                                        </th>
                                        <th class="border-0 px-4 py-3">
                                            <i class="bi bi-link-45deg me-1"></i>RTSP URL
                                        </th>
                                        <th class="border-0 px-4 py-3">
                                            <i class="bi bi-cpu-fill me-1"></i>Recognition Model
                                        </th>
                                        <th class="border-0 px-4 py-3">
                                            <i class="bi bi-activity me-1"></i>Status
                                        </th>
                                        <th class="border-0 px-4 py-3 text-center">
                                            <i class="bi bi-gear-fill me-1"></i>Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for camera in cameras %}
                                    <tr>
                                        <td class="px-4 py-3">
                                            <div class="d-flex align-items-center">
                                                <div class="bg-primary bg-gradient rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 32px; height: 32px;">
                                                    <i class="bi bi-camera-fill text-white small"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-semibold">{{ camera.name }}</div>
                                                    <small class="text-muted">Camera #{{ camera.id }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <code class="bg-light px-2 py-1 rounded">{{ camera.rtsp_url|truncatechars:40 }}</code>
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="badge bg-info">{{ camera.get_recognizer_display }}</span>
                                        </td>
                                        <td class="px-4 py-3">
                                            {% if camera.is_streaming %}
                                                <span class="badge bg-success">
                                                    <i class="bi bi-play-circle-fill me-1"></i>Streaming
                                                </span>
                                            {% else %}
                                                <span class="badge bg-secondary">
                                                    <i class="bi bi-pause-circle-fill me-1"></i>Not Streaming
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="d-flex justify-content-center flex-wrap gap-1">
                                                <a href="{% url 'cameras:update' camera.id %}" 
                                                   class="btn btn-outline-primary btn-sm" 
                                                   data-bs-toggle="tooltip" 
                                                   title="Edit Camera">
                                                    <i class="bi bi-pencil-square"></i>
                                                </a>
                                                <a href="{% url 'videostream:stream_viewer' %}?camera_url={{ camera.rtsp_url|urlencode }}&camera_id={{ camera.id }}" 
                                                   class="btn btn-outline-success btn-sm" 
                                                   target="_blank"
                                                   data-bs-toggle="tooltip" 
                                                   title="View Stream">
                                                    <i class="bi bi-play-circle-fill"></i>
                                                </a>
                                                <a href="{% url 'videostream:zone_creator' %}?camera_url={{ camera.rtsp_url|urlencode }}&camera_id={{ camera.id }}" 
                                                   class="btn btn-outline-warning btn-sm" 
                                                   target="_blank"
                                                   data-bs-toggle="tooltip" 
                                                   title="Zone Creator">
                                                    <i class="bi bi-bounding-box"></i>
                                                </a>
                                                <a href="{% url 'videostream:view_zones' %}?camera_url={{ camera.rtsp_url|urlencode }}&camera_id={{ camera.id }}" 
                                                   class="btn btn-outline-info btn-sm" 
                                                   target="_blank"
                                                   data-bs-toggle="tooltip" 
                                                   title="View Zones">
                                                    <i class="bi bi-eye-fill"></i>
                                                </a>
                                                <a href="{% url 'cameras:delete' camera.id %}" 
                                                   class="btn btn-outline-danger btn-sm" 
                                                   data-bs-toggle="tooltip" 
                                                   title="Delete Camera"
                                                   onclick="return confirm('Are you sure you want to delete this camera?')">
                                                    <i class="bi bi-trash-fill"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-camera-video text-muted mb-3" style="font-size: 4rem;"></i>
                            <h5 class="text-muted mb-3">No Cameras Found</h5>
                            <p class="text-muted mb-4">Get started by adding your first security camera to the system.</p>
                            <a href="{% url 'cameras:add' %}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>Add Your First Camera
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Enable tooltips
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}