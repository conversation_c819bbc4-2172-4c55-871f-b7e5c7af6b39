import numpy as np
from PIL import Image
from django.core.files.base import ContentFile
from io import BytesIO
from django.utils import timezone

from django.forms import formset_factory
from django.http import HttpResponse, HttpResponseForbidden
from django.contrib.auth.models import User
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate, logout
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.urls import reverse
from django.contrib.auth.views import LoginView

from alerts.utils import detect_faces
from .forms import is_admin_user
from datetime import datetime

from .models import UserProfile
from .forms import RegisterForm, UserProfileForm, UserUpdateForm
from recognition.arcface_recognizer import ArcFaceRecognizer


@login_required
def update_profile(request):
    if request.method == 'POST':
        user_form = UserUpdateForm(request.POST, instance=request.user)
        profile_form = UserProfileForm(request.POST, instance=request.user.userprofile)
        if user_form.is_valid() and profile_form.is_valid():
            user_form.save()
            profile_form.save()
            messages.success(request, 'Your profile has been updated successfully.')
            return redirect('index')
    else:
        user_form = UserUpdateForm(instance=request.user)
        if not hasattr(request.user, 'userprofile'):
            UserProfile.objects.create(user=request.user, preferred_recognizer='arcface')
        profile_form = UserProfileForm(instance=request.user.userprofile)
    return render(request, 'users/update_profile.html', {'user_form': user_form, 'profile_form': profile_form})

def index(request):
    return HttpResponse("Users index.")


def register_view(request):
    if request.method == "POST":
        form = RegisterForm(request.POST)
        if form.is_valid():
            user = form.save(commit=False)
            user.set_password(form.cleaned_data['password'])
            is_admin = not UserProfile.objects.exists()
            user.save()
            UserProfile.objects.create(
                user=user,
                preferred_recognizer='arcface',  # Default recognizer is ArcFace
                is_organizational_admin=is_admin
            )
            login(request, user, backend='django.contrib.auth.backends.ModelBackend')
            return redirect('index')
    else:
        form = RegisterForm()
    return render(request, 'users/register.html', {'form': form})


@login_required
def delete_account(request):
    if request.method == 'POST':
        request.user.delete()
        return redirect('index')
    return render(request, 'users/confirm_delete_account.html')

class CustomLoginView(LoginView):
    template_name = 'users/login.html'


@login_required
def dashboard(request):
    """Kullanıcı dashboard'ına yönlendir"""
    return redirect('index')  # Normal kullanıcı için anasayfaya yönlendir

