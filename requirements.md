| Requirement | Status |
|------------|---------|
| User can sign up | DONE |
| User can sign in | DONE |
| User can sign out | DONE |
| User can reset password | DONE |
| User can update profile | DONE |
| User can delete account | DONE |
| During registration verification email will be send. Also verification sms will be send. Both of them must be verified. | NEW |
| If user is inactive for 5 minutes, he is logged out. | NEW |
| User can view camera list | DONE |
| User can add camera. It includes camera name, rtsp url, and camera type | DONE |
| User can update camera | DONE |
| User can delete camera | DONE |
| Camera list shows if each camera is streaming or not | DONE |
| User can view alert person names as a list | DONE |
| The alert person list is paginated, showing a specified number of names per page | DONE |
| User can navigate between pages to view more alert person names | DONE |
| User can query alert photo by name | DONE |
| User can add alert person name | DONE |
| Uer can add multiple photos for the same name | DONE |
| User can delete alert user and his/her photo(s) | DONE |
| Uploaded photos will be saved in the django storage | DONE |
| Uploaded photos shall be larger or equal to defined size (width x height) | DONE |
| System will calculate the image vector by using ArcFace model and save it in the database | DONE |
| User can list and filter the generated alarms | DONE |
| Generted alarms has date, camera name, and person name and video snapshot of the related duration | DONE |
| User can view the alarm video snapshot when clicking on the alarm | NEW |
| Alarms will be deleted after 1 day (this is adjustable admin requirement) | NEW |
| aiortc is used for video streaming | DONE |
| django is used for user management, camera management, and alert management | DONE |
| opencv is used for image processing | DONE |
| pillow is used for image processing | DONE |
| django-storages is used for image storage | DONE |
| django-filter is used for filtering | DONE |
| django-rest-framework is used for api | DONE |
| django-cors-headers is used for cors | DONE |
| django-debug-toolbar is used for debugging | DONE |
| ArcFace model is used for face recognition | DONE |
| SCRFD model is used for face detection | DONE |
| ONNX is used for model inference | DONE |
| APIs are RESTful | DONE |
| APIs are versioned | NEW |
| APIs are paginated | DONE |
| APIs are filterable | DONE |
| APIs are sortable | NEW |
| APIs are searchable | NEW |
| APIs are secure | NEW |
| APIs are scalable | NEW |
| APIs are maintainable | NEW |
| APIs are testable | NEW |
| Unit test cases are written for all models, views, and serializers | NEW |
| Integration test cases are written for all views | NEW |
| End-to-end test cases are written for all views | NEW |
| Face recogintion and detection models can be changed dynamically | DONE |
| If there is a detected face and it can't be found in the facebank then record it as a unkown new person, give it a unique name. | DONE |
| Unknown detected face size should be bigger than 64 pixels. | DONE |
| If detected face matches with an unknown person do not generate alarm but record it in the database. So in the database there will be alarms and repeated unalarmed events. | DONE |
| If there are multiple people in a frame record one video but link to all the people in the same frame. | NEW |
| Every detected face instance of a person will be recorded in the facebank so it will improve the facebank accuracy. | DONE |
| Site admin manages the subscription levels. | DONE |
| Site admin enters flats, their numbers, tenants, their user names, first passwords, emails. | NEW |
| Site admin creates the main facebank data for each resident. | DONE |
| Residents can only see their flats photos and crud their guest photos. guest photos can be seen by site admin.Guest photos shall be recorded with name. | DONE |
| Optional. Allowed guest entry exit date/times can be entered for each guest. | DONE |
| What subscription levels need to be supported? | NEW |
| What features should be available at each subscription level? | NEW |
| Should flats/tenants be limited based on subscription level? | NEW |
| How long should historical data be retained? | NEW |
| Should there be a way to transfer guest lists between tenants? | NEW |
| What should happen to guest data when a tenant moves out? | NEW |
| What level of access should site admins have to tenant data? | NEW |
| Should there be different admin roles? | NEW |
| How should camera access be managed between flats? | NEW |
| API Rate Limiting | NEW |
| Error Handling Standards | NEW |
| Logging Requirements | NEW |
| Deployment Architecture | NEW |
| Performance Requirements | NEW |
| Monitoring/Alerting Requirements | NEW |
| Should flats have additional attributes beyond just numbers? | NEW |
| Do we need to track flat occupancy history? | NEW |
| Can a flat have multiple tenants simultaneously? | NEW |
| Should there be a way to mark flats as vacant/occupied? | NEW |
| What information needs to be stored for tenants beyond username, password, and email? | NEW |
| Should the system support tenant family members with separate accounts? | NEW |
| How should tenant turnover be handled (moving out/in)? | NEW |
| Should there be a distinction between primary and secondary tenants? | NEW |
| How should guest photos be organized (per flat or per tenant)? | NEW |
| Should there be a limit on the number of guests per flat/tenant? | NEW |
| Should recurring visits be supported? | NEW |
| Should there be a maximum duration for guest access? | NEW |
| Should notifications be sent when guest access expires? | NEW |
| Password Requirements/Complexity | NEW |
| Email Verification Process | NEW |
| Profile Fields That Can Be Updated | NEW |
| Session Management & Timeout | NEW |
| Supported Camera Types | NEW |
| RTSP URL Validation | NEW |
| Maximum Allowed Cameras Per User | NEW |
| What Streaming Status Means | NEW |
| Minimum/Maximum Photo Size | NEW |
| Accepted Photo Formats | NEW |
| Maximum Photos Per Person | NEW |
| Alarm Pagination Size | NEW |
| Alarm Filtering Criteria | NEW |
| Video Snapshot Duration | NEW |
| How Alarms Are Generated | NEW |
| How Are Unique Names Generated for Unknown Faces? | NEW |
| Confidence Threshold for Face Matching | NEW |
| How Improving Facebank Accuracy Works | NEW |
| Maximum Faces Per Frame | NEW |
| Password Reset Process for Tenants | NEW |
| Email Notification System | NEW |
| Maximum Allowed Photos Per Resident | NEW |
| Guest Photo Approval Process | NEW |
| Payment Processing System | NEW |
| Subscription Renewal Process | NEW |
| Upgrade/Downgrade Handling | NEW |
| Trial Period / Free Tier | NEW |
| Backup/Restore Procedures | NEW |
| Data Export Formats | NEW |
| Audit Logging Requirements | NEW |
| Data Retention After Subscription Cancellation | NEW |
| Session Timeout Periods | NEW |
| Failed Login Attempt Handling | NEW |
| IP-Based Access Restrictions | NEW |
| Concurrent Login Handling | NEW |
| API Rate Limiting | NEW |
| Error Handling Standards | NEW |
| Logging Requirements | NEW |
| Deployment Architecture | NEW |
| Performance Requirements | NEW |
| Monitoring/Alerting Requirements | NEW |
| Camera Locations Setting | NEW |
| Camera Stream Quality Settings | NEW |
| Camera Health Monitoring | NEW |
| Door Control System Integration | NEW |
| Notification Delivery Preferences | NEW |
| Third-Party Integration Capabilities | NEW |
| Available Advanced Reports | NEW |
| Report Scheduling | NEW |
| Custom Report Builder | NEW |
| Load Monitoring Thresholds | NEW |
| Load Balancing Configuration | NEW |
| Maximum Concurrent Video Streams | NEW |
| Subscription Cancellation Process | NEW |
| Handling Overdue Payments | NEW |
| Subscription Transfer Between Organizations | NEW |
| Invitation Link Validity | NEW |
| Bulk Tenant Import | NEW |
| Tenant Verification Beyond Email/SMS | NEW |
| Maximum Buildings Per Site | NEW |
| Building Hierarchy Management | NEW |
| Building Access Schedules | NEW |
