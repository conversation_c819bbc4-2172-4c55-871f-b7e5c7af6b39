from django.contrib import admin
from .models import <PERSON><PERSON><PERSON><PERSON>, AlertPhoto, Alarm

@admin.register(AlertPerson)
class AlertPersonAdmin(admin.ModelAdmin):
    list_display = ['name', 'user', 'is_unknown', 'last_seen_date', 'created_at', 'updated_at']
    list_filter = ['is_unknown', 'user', 'created_at']
    search_fields = ['name', 'user__username']
    date_hierarchy = 'created_at'

@admin.register(AlertPhoto)
class AlertPhotoAdmin(admin.ModelAdmin):
    list_display = ['person', 'is_primary', 'is_multiple_face', 'created_at']
    list_filter = ['is_primary', 'is_multiple_face', 'created_at']
    search_fields = ['person__name']
    date_hierarchy = 'created_at'



@admin.register(Alarm)
class AlarmAdmin(admin.ModelAdmin):
    list_display = ['person', 'camera', 'date', 'confidence', 'alert_photo']
    list_filter = ['camera', 'person', 'date', 'alert_photo']
    search_fields = ['person__name', 'camera__name']
    date_hierarchy = 'date'