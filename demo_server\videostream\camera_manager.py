import threading
import logging
from videostream.rtsp_camera import RTSPCamera

logger = logging.getLogger(__name__)

class CameraManager:
    def __init__(self):
        self.cameras = {}  # camera_id -> RTSPCamera nesnesi
        self.lock = threading.Lock()
    
    def start_stream(self, camera_id, url, camera_obj=None):
        """Start RTSP stream with given URL for a specific camera"""
        with self.lock:
            if camera_id in self.cameras:
                self.cameras[camera_id].stop_stream()
                
            self.cameras[camera_id] = RTSPCamera()
            self.cameras[camera_id].start_stream(url, camera_obj)
    
    def stop_stream(self, camera_id):
        """Stop RTSP stream for a specific camera"""
        with self.lock:
            if camera_id in self.cameras:
                self.cameras[camera_id].stop_stream()
                del self.cameras[camera_id]
    
    def stop_all_streams(self):
        """Stop all streams gracefully"""
        with self.lock:
            camera_ids = list(self.cameras.keys())
            logger.info(f"Stopping {len(camera_ids)} active streams")

            for camera_id in camera_ids:
                try:
                    logger.info(f"Stopping stream for camera {camera_id}")
                    self.cameras[camera_id].stop_stream()
                    del self.cameras[camera_id]
                    logger.info(f"Successfully stopped stream for camera {camera_id}")
                except Exception as e:
                    logger.error(f"Error stopping stream for camera {camera_id}: {e}")

            logger.info("All streams stopped")
    
    def get_frame(self, camera_id):
        """Get the current frame from a specific camera"""
        with self.lock:
            if camera_id in self.cameras:
                return self.cameras[camera_id].get_frame()
        return None


# Global camera manager instance
camera_manager = CameraManager()
