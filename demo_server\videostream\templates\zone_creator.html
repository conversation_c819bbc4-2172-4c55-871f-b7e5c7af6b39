{% extends 'stream_viewer.html' %}
{% load static %}
{% block title %}Zone Creator{% endblock %}
{% block header_title %}<i class="fas fa-draw-polygon"></i> Zone Creator{% endblock %}
{% block controls_panel %}
<div class="control-group">
    <h3>Stream Controls</h3>
    <input type="hidden" id="stream-url" value="{{ stream_url|safe }}">
    <input type="hidden" id="camera-id" value="{{ camera_id|default:'' }}">
    <div class="button-group">
        <button class="start-btn" onclick="startStream()">
            <i class="fas fa-play"></i> Start
        </button>
        <button class="stop-btn" onclick="stopStream()">
            <i class="fas fa-stop"></i> Stop
        </button>
    </div>
</div>
<div class="control-group">
  <h3>Debug Information</h3>
  <div id="debug-info">Debug information will appear here</div>
</div>

<div class="control-group">
    <button id="create">Create Polygon</button>
    <button id="save-zones">Save Zones</button>
    <div class="areas">
        <h3>Areas</h3>
        <div id="area-list"></div>
    </div>
</div>
{% endblock %}
{% block extra_js %}
<script src="{% static 'videostream/js/canvas_manager.js' %}"></script>
<script src="{% static 'videostream/js/polygon.js' %}"></script>
<script src="{% static 'videostream/js/sidebar_manager.js' %}"></script>
<script src="{% static 'videostream/js/canvas_interaction_handler.js' %}"></script>
<script>
  let polygons = []; // Move polygons to the global scope

  window.onload = async function () {
    const canvas = document.getElementById("draw-canvas");
    const video = document.getElementById("stream");
    const cameraId = document.getElementById("camera-id").value;

    // activateZoneFilter'i çağır
    setTimeout(() => {
      activateZoneFilter(cameraId);
    }, 1000);

    const canvasManager = new CanvasManager(canvas);
    const areaList = document.getElementById("area-list");

    let currentPolygon = null;
    let polygonCount = 0;

    function updateCanvasPosition() {
      if (video.complete && video.naturalWidth !== 0) {
        canvasManager.resizeToMatch(video);
        canvasManager.draw();
      }
    }

    video.addEventListener('load', updateCanvasPosition);
    video.addEventListener('loadeddata', updateCanvasPosition);
    video.addEventListener('resize', updateCanvasPosition);

    if (video.complete) {
      updateCanvasPosition();
    }

    setInterval(updateCanvasPosition, 1000);
    setTimeout(updateCanvasPosition, 500);

    const sidebar = new SidebarManager(
      areaList,
      (polygon) => {
        currentPolygon = polygon;
        canvasManager.setPolygons(polygons, currentPolygon);
        canvasManager.draw();
      },
      (polygon, div) => {
        polygons = polygons.filter(p => p !== polygon);
        if (currentPolygon === polygon) currentPolygon = null;
        sidebar.removePolygonLabel(div);
        canvasManager.setPolygons(polygons, currentPolygon);
        canvasManager.draw();
      }
    );

    async function fetchZones() {
      try {
        const response = await fetch(`/videostream/get_zones/?camera_id=${cameraId}`);
        const result = await response.json();
        if (response.ok) {
          const videoWidth = video.clientWidth;
          const videoHeight = video.clientHeight;

          polygons = (result.zones || []).map(zone => {
            const polygon = new Polygon(zone.color);
            polygon.name = zone.name;
            polygon.points = zone.points.map(point => ({
              x: point.x * videoWidth,
              y: point.y * videoHeight
            }));
            polygon.complete = true;
            sidebar.addPolygonLabel(zone.name, zone.color, polygon);
            return polygon;
          });

          canvasManager.setPolygons(polygons);
          canvasManager.draw();
        } else {
          console.error(`Error fetching zones: ${result.error}`);
        }
      } catch (error) {
        console.error("Error fetching zones:", error);
      }
    }

    function syncSizeAndDraw() {
      canvasManager.resizeToMatch(video);

      document.getElementById("create").addEventListener("click", () => {
        if (currentPolygon && currentPolygon.points.length > 2) {
          currentPolygon.complete = true;
        }

        const color = "#" + Math.floor(Math.random() * 16777215).toString(16);
        polygonCount++;

        currentPolygon = new Polygon(color);
        currentPolygon.name = `Polygon ${polygonCount}`;
        polygons.push(currentPolygon);

        sidebar.addPolygonLabel(currentPolygon.name, color, currentPolygon);
        canvasManager.setPolygons(polygons, currentPolygon);
        canvasManager.draw();

        const interactionHandler = new CanvasInteractionHandler(
          canvas,
          () => currentPolygon,
          () => canvasManager.draw()
        );
      });
    }

    setTimeout(() => {
      if (video.complete) {
        syncSizeAndDraw();
        fetchZones();
      } else {
        video.onload = () => {
          syncSizeAndDraw();
          fetchZones();
        };
      }
    }, 3000);
  };

  // Save button event
  document.getElementById("save-zones").addEventListener("click", async () => {
    const cameraId = document.getElementById("camera-id").value;
    const video = document.getElementById("stream");

    const videoWidth = video.videoWidth || video.clientWidth;
    const videoHeight = video.videoHeight || video.clientHeight;

    const zones = polygons.map(polygon => {
      const normalizedPoints = polygon.points.map(point => ({
        x: point.x / videoWidth,
        y: point.y / videoHeight
      }));

      return {
        name: polygon.name || `Polygon ${polygon.id}`,
        points: normalizedPoints,
        color: polygon.color
      };
    });

    try {
      const response = await fetch("{% url 'videostream:save_zones' %}", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": "{{ csrf_token }}"
        },
        body: JSON.stringify({ camera_id: cameraId, zones })
      });

      const result = await response.json();
      if (response.ok) {
        alert("Zones saved successfully!");
      } else {
        alert(`Error saving zones: ${result.error}`);
      }
    } catch (error) {
      console.error("Error saving zones:", error);
      alert("An error occurred while saving zones.");
    }
  });

</script>
{% endblock %}