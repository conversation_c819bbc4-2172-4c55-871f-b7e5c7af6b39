from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

app_name = 'alerts'

urlpatterns = [
    path('', views.index, name='index'),
    path('delete-multiple-alerts/', views.delete_multiple_alerts, name='delete_multiple_alerts'),
    path('persons/', views.alert_person_list, name='person_list'),
    path('persons/delete-selected/', views.delete_selected_persons, name='delete_selected_persons'),
    path('person/add/', views.add_alert_person, name='add_person'),
    path('person/<int:person_id>/', views.person_detail, name='person_detail'),
    path('photos/', views.alert_photo_list, name='photo_list'),
    path('photos/<int:photo_id>/', views.alert_photo_detail, name='photo_detail'),
    path('photos/add/', views.add_alert_photo, name='add_photo'),
    path('photos/delete/<int:photo_id>/', views.delete_photo, name='delete_photo'),
    path('persons/delete/<int:user_id>/', views.delete_alert_user, name='delete_user'),
    path('alerts/', views.alert_list, name='alert_list'),
    path('alerts/delete/<int:alert_id>/', views.delete_alert, name='delete_alert'),
    path('unknown_persons/', views.unknown_persons_list, name='unknown_persons_list'),
    path('unknown_persons/<int:person_id>/', views.unknown_person_detail, name='unknown_person_detail'),
    path('unknown_persons/<int:person_id>/rename/', views.rename_unknown_person, name='rename_unknown_person'),
    path('cleanup-orphaned-embeddings/', views.cleanup_orphaned_embeddings, name='cleanup_orphaned_embeddings'),
]