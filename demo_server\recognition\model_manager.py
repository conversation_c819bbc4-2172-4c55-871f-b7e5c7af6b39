import logging
import threading
import atexit
import os
import sys
import traceback
import importlib

logger = logging.getLogger(__name__)

class ModelManager:
    """
    Model Manager sınıfı - Singleton desenli modelleri tek yerden yöneten merkezi yapı.
    Tüm model yükleme işlemleri burada yapılır ve modeller bellekte tek bir yerde saklanır.
    """
    _instance = None
    _init_lock = threading.Lock()
    _model_instances = {}
    _model_classes = {}
    _model_components = {}  # Model bileşenlerini saklar
    _process_id = os.getpid()

    def __new__(cls):
        with cls._init_lock:
            if cls._instance is None:
                logger.info(f"==== MODEL MANAGER İLK KEZ OLUŞTURULUYOR (Process: {cls._process_id}) ====")
                cls._instance = super(ModelManager, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        # İlk kez oluşturulduysa initialize et
        if not self._initialized:
            logger.info(f"ModelManager başlatılıyor (Process: {self._process_id})")
            self._initialized = True

    def _import_model_class(self, model_type):
        """Model sınıfını gerektiğinde import eder"""
        if model_type not in self._model_classes:
            logger.info(f"Importing model class for '{model_type}' (Process: {self._process_id})")
            try:
                if model_type == 'arcface':
                    module = importlib.import_module('.arcface_recognizer', package='recognition')
                    self._model_classes[model_type] = module.ArcFaceRecognizer
                    logger.info(f"ArcFaceRecognizer class imported successfully (Process: {self._process_id})")
                elif model_type == 'facenet':
                    module = importlib.import_module('.facenet_recognizer', package='recognition')
                    self._model_classes[model_type] = module.FaceNetRecognizer
                    logger.info(f"FaceNetRecognizer class imported successfully (Process: {self._process_id})")
                else:
                    raise ValueError(f"Unknown model type: {model_type}")
            except Exception as e:
                logger.error(f"Error importing model class for '{model_type}': {str(e)}")
                logger.error(traceback.format_exc())
                raise

    def _create_model_components(self, model_type):
        """Model bileşenlerini oluşturur (detector, recognizer, mtcnn, vs.)"""
        if model_type not in self._model_components:
            logger.info(f"Creating model components for '{model_type}' (Process: {self._process_id})")
            
            try:
                # Model sınıfı içine geçmeden önce gerekli bileşenleri yükle
                if model_type == 'arcface':
                    # ArcFace için SCRFD detector ve ArcFaceONNX tanıyıcı yükleme
                    from .arcface_scrfd import SCRFD
                    from .arcface_onnx import ArcFaceONNX
                    from django.conf import settings
                    import os
                    
                    conf = settings.RECOGNITION_CONFIG
                    model_path = os.path.expanduser(conf['model_path'])
                    
                    # Detector yükle
                    det_model_path = os.path.join(model_path, 'buffalo_l/det_10g.onnx')
                    detector = SCRFD(det_model_path)
                    detector.prepare(0, use_cuda=True)
                    
                    # Recognizer yükle
                    rec_model_path = os.path.join(model_path, 'buffalo_l/w600k_r50.onnx')
                    recognizer = ArcFaceONNX(rec_model_path)
                    recognizer.prepare(0, use_cuda=True)
                    
                    self._model_components[model_type] = {
                        'detector': detector,
                        'recognizer': recognizer
                    }
                    logger.info(f"ArcFace components created successfully")
                    
                elif model_type == 'facenet':
                    # FaceNet için MTCNN ve Backbone model yükleme
                    from .facenet_mtcnn import MTCNN
                    from .facenet_model import Backbone, MobileFaceNet
                    from django.conf import settings
                    import os
                    import torch
                    
                    conf = settings.RECOGNITION_CONFIG
                    
                    # MTCNN yükle
                    mtcnn = MTCNN()
                    
                    # Model yükle
                    if conf['use_mobilfacenet']:
                        model = MobileFaceNet(conf['embedding_size']).to(conf['device'])
                    else:
                        model = Backbone(conf['net_depth'], conf['drop_ratio'], conf['net_mode']).to(conf['device'])
                    
                    model_name = 'ir_se50.pth'  # Default model name
                    model_path = os.path.join(os.getcwd(), conf['model_path'], f'model_{model_name}')
                    model.load_state_dict(torch.load(model_path, map_location=torch.device(conf['device'])))
                    model.eval()
                    
                    self._model_components[model_type] = {
                        'mtcnn': mtcnn,
                        'model': model
                    }
                    logger.info(f"FaceNet components created successfully")
                    
                else:
                    raise ValueError(f"Unknown model type for components: {model_type}")
                    
            except Exception as e:
                logger.error(f"Error creating model components for '{model_type}': {str(e)}")
                logger.error(traceback.format_exc())
                raise
                
        return self._model_components[model_type]

    def _create_model_instance(self, model_type):
        """Model sınıfından yeni bir instance oluşturur"""
        self._import_model_class(model_type)
        
        logger.info(f"Creating model instance for '{model_type}' (Process: {self._process_id})")
        try:
            # Önce bileşenleri oluştur ve dependency injection ile modele geç
            components = self._create_model_components(model_type)
            
            if model_type == 'arcface':
                model_instance = self._model_classes[model_type](
                    detector=components['detector'],
                    recognizer=components['recognizer']
                )
            elif model_type == 'facenet':
                model_instance = self._model_classes[model_type](
                    mtcnn=components['mtcnn'],
                    model=components['model']
                )
            else:
                model_instance = self._model_classes[model_type]()
                
            return model_instance
        except Exception as e:
            logger.error(f"Error creating model instance for '{model_type}': {str(e)}")
            logger.error(traceback.format_exc())
            raise

    def get_model(self, model_type='arcface'):
        """
        Belirtilen model türünü döndürür. 
        Eğer model henüz yüklenmemişse yükler ve bellekte saklar.
        """
        # Bilinmeyen model türü için varsayılan seç
        if model_type not in ['arcface', 'facenet']:
            logger.warning(f"Bilinmeyen model türü: {model_type}, varsayılan olarak arcface kullanılıyor")
            model_type = 'arcface'
        
        with self._init_lock:
            # İlgili model zaten yüklenmişse onu döndür
            if model_type in self._model_instances:
                logger.debug(f"Model '{model_type}' zaten yüklü, mevcut model örneği döndürülüyor (Process: {self._process_id})")
                return self._model_instances[model_type]
            
            # Model yüklenmemişse şimdi yükle
            logger.info(f"Model '{model_type}' henüz yüklenmemiş, yükleniyor... (Process: {self._process_id})")
            try:
                self._model_instances[model_type] = self._create_model_instance(model_type)
                logger.info(f"Model '{model_type}' başarıyla yüklendi (Process: {self._process_id})")
                return self._model_instances[model_type]
            except Exception as e:
                logger.error(f"Model '{model_type}' yüklenirken hata: {str(e)}")
                logger.error(traceback.format_exc())
                raise

    def get_loaded_models(self):
        """Yüklü model tiplerini ve durumlarını döndürür"""
        return {model_type: "Loaded" for model_type in self._model_instances}

    def cleanup(self):
        """Uygulama kapatıldığında kaynakları serbest bırak"""
        logger.info(f"Modellerin kaynakları serbest bırakılıyor... (Process: {self._process_id})")
        self._model_instances.clear()
        self._model_components.clear()
        self._model_classes.clear()
        self._initialized = False

# ----------------------------------------------------------------------------------
# Aşağıdaki fonksiyonlar, sınıf singleton API'sini çağıran yardımcı fonksiyonlardır.
# Mevcut kodlarla uyumluluk sağlamak için kullanılıyorlar.
# ----------------------------------------------------------------------------------

# Singleton model manager örneği
_model_manager = ModelManager()

def get_model(model_type='arcface'):
    """ModelManager sınıfına kolay erişim için fonksiyon"""
    return _model_manager.get_model(model_type)

def get_loaded_models():
    """ModelManager sınıfına kolay erişim için fonksiyon"""
    return _model_manager.get_loaded_models()

# Uygulama çıkışında cleanup'ı otomatik çağır
atexit.register(_model_manager.cleanup)

# NOT: Artık modüller, burada import sırasında otomatik olarak yüklenmez.
# Her bir model, sadece ilk kez istendiğinde yüklenir (lazy-loading). 