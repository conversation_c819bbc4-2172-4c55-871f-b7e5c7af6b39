{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <h2>{{ person.name }} - Entry/Exit Times</h2>
            <p class="text-muted">Showing data for {{ selected_date|date:"F d, Y" }}</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'alerts:person_list' %}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to Persons
            </a>
        </div>
    </div>

    <!-- Date Selection -->
    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title">Select Date</h5>
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <input type="date" 
                           name="date" 
                           value="{{ selected_date|date:'Y-m-d' }}" 
                           class="form-control"
                           onchange="this.form.submit()">
                </div>
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <a href="?date={% now 'Y-m-d' %}" class="btn btn-outline-primary btn-sm">Today</a>
                        <a href="?date={% now 'Y-m-d'|add:'-1 day' %}" class="btn btn-outline-primary btn-sm">Yesterday</a>
                        <a href="?date={% now 'Y-m-d'|add:'-7 days' %}" class="btn btn-outline-primary btn-sm">Week Ago</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Times Data -->
    {% if has_data %}
        <div class="row">
            {% for camera_name, data in camera_summary.items %}
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-camera"></i> {{ camera_name }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <h6 class="text-success">
                                    <i class="bi bi-box-arrow-in-right"></i> Entry
                                </h6>
                                {% if data.entry_time %}
                                    <p class="h4 text-success">{{ data.entry_time.date|time:"H:i" }}</p>
                                    <small class="text-muted">
                                        Confidence: {{ data.entry_time.confidence|floatformat:2 }}
                                    </small>
                                {% else %}
                                    <p class="text-muted">No entry recorded</p>
                                {% endif %}
                            </div>
                            <div class="col-6">
                                <h6 class="text-danger">
                                    <i class="bi bi-box-arrow-right"></i> Exit
                                </h6>
                                {% if data.exit_time %}
                                    <p class="h4 text-danger">{{ data.exit_time.date|time:"H:i" }}</p>
                                    <small class="text-muted">
                                        Confidence: {{ data.exit_time.confidence|floatformat:2 }}
                                    </small>
                                {% else %}
                                    <p class="text-muted">Still inside</p>
                                {% endif %}
                            </div>
                        </div>
                        
                        {% if data.exit_time and data.entry_time %}
                        <hr>
                        <div class="text-center">
                            <small class="text-muted">
                                Duration: 
                                {% with duration=data.exit_time.date|timesince:data.entry_time.date %}
                                    {{ duration }}
                                {% endwith %}
                            </small>
                        </div>
                        {% endif %}
                        
                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-info" 
                                    type="button" 
                                    data-bs-toggle="collapse" 
                                    data-bs-target="#details-{{ forloop.counter }}" 
                                    aria-expanded="false">
                                <i class="bi bi-list-ul"></i> View All Detections ({{ data.total_detections }})
                            </button>
                        </div>

                        <div class="collapse mt-3" id="details-{{ forloop.counter }}">
                            <div class="card card-body">
                                <h6>All Detections:</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>Time</th>
                                                <th>Confidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for alarm in data.all_alarms %}
                                            <tr>
                                                <td>{{ alarm.date|time:"H:i:s" }}</td>
                                                <td>
                                                    <span class="badge bg-{% if alarm.confidence > 0.9 %}success{% elif alarm.confidence > 0.7 %}warning{% else %}danger{% endif %}">
                                                        {{ alarm.confidence|floatformat:2 }}
                                                    </span>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-info text-center" role="alert">
            <i class="bi bi-info-circle"></i>
            <h4>No Records Found</h4>
            <p>No entry/exit records found for <strong>{{ person.name }}</strong> on {{ selected_date|date:"F d, Y" }}.</p>
            <hr>
            <p class="mb-0">Try selecting a different date or check if the person has been detected by any camera.</p>
        </div>
    {% endif %}
</div>

                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> View Times
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Results Section -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        Times for {{ selected_date|date:'F d, Y' }}
                    </h5>
                </div>
                <div class="card-body">
                    {% if camera_summary %}
                        <div class="row">
                            {% for camera_name, data in camera_summary.items %}
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">
                                            <i class="bi bi-camera-video"></i> {{ camera_name }}
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        {% if data.first_entry or data.last_exit %}
                                            <div class="mb-3">
                                                {% if data.first_entry %}
                                                <div class="d-flex align-items-center mb-2">
                                                    <span class="badge bg-success me-2">
                                                        <i class="bi bi-box-arrow-in-right"></i> Entry
                                                    </span>
                                                    <strong>{{ data.first_entry.timestamp|time:'H:i' }}</strong>
                                                    {% if data.first_entry.confidence %}
                                                        <small class="text-muted ms-2">
                                                            ({{ data.first_entry.confidence|floatformat:2 }})
                                                        </small>
                                                    {% endif %}
                                                </div>
                                                {% endif %}
                                                
                                                {% if data.last_exit %}
                                                <div class="d-flex align-items-center mb-2">
                                                    <span class="badge bg-danger me-2">
                                                        <i class="bi bi-box-arrow-right"></i> Exit
                                                    </span>
                                                    <strong>{{ data.last_exit.timestamp|time:'H:i' }}</strong>
                                                    {% if data.last_exit.confidence %}
                                                        <small class="text-muted ms-2">
                                                            ({{ data.last_exit.confidence|floatformat:2 }})
                                                        </small>
                                                    {% endif %}
                                                </div>
                                                {% endif %}
                                            </div>

                                            <!-- Duration calculation if both entry and exit exist -->
                                            {% if data.first_entry and data.last_exit %}
                                            <div class="alert alert-info small">
                                                <i class="bi bi-clock"></i>
                                                Duration: 
                                                {% with duration=data.last_exit.timestamp|timesince:data.first_entry.timestamp %}
                                                    {{ duration }}
                                                {% endwith %}
                                            </div>
                                            {% endif %}

                                            <!-- Show all entries/exits if more than one -->
                                            {% if data.all_entries|length > 1 or data.all_exits|length > 1 %}
                                            <div class="mt-3">
                                                <button class="btn btn-sm btn-outline-secondary" 
                                                        type="button" 
                                                        data-bs-toggle="collapse" 
                                                        data-bs-target="#details-{{ forloop.counter }}" 
                                                        aria-expanded="false">
                                                    <i class="bi bi-list"></i> Show All Events
                                                </button>
                                                <div class="collapse mt-2" id="details-{{ forloop.counter }}">
                                                    <div class="small">
                                                        {% for entry in data.all_entries %}
                                                        <div class="text-success">
                                                            <i class="bi bi-box-arrow-in-right"></i> 
                                                            Entry: {{ entry.timestamp|time:'H:i:s' }}
                                                        </div>
                                                        {% endfor %}
                                                        {% for exit in data.all_exits %}
                                                        <div class="text-danger">
                                                            <i class="bi bi-box-arrow-right"></i> 
                                                            Exit: {{ exit.timestamp|time:'H:i:s' }}
                                                        </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>
                                            {% endif %}
                                        {% else %}
                                            <div class="text-muted text-center">
                                                <i class="bi bi-info-circle"></i>
                                                <p class="mb-0">No activity recorded</p>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-5">
                            <i class="bi bi-calendar-x" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">No Activity Found</h5>
                            <p>No entry/exit records found for {{ person.name }} on {{ selected_date|date:'F d, Y' }}.</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Date Selection -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">Quick Date Selection</h6>
                </div>
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <a href="?date={% now 'Y-m-d' %}" class="btn btn-sm btn-outline-primary">Today</a>
                        <a href="?date={% now 'Y-m-d'|date:'Y-m-d' %}" class="btn btn-sm btn-outline-primary">Yesterday</a>
                        <a href="?date={% now 'Y-m-d'|date:'Y-m-d' %}" class="btn btn-sm btn-outline-primary">This Week</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge {
    font-size: 0.75em;
}
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.card-header {
    border-bottom: 1px solid #e9ecef;
}
</style>
{% endblock %}
